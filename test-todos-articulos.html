<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Test Todos los Artículos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
        .expected-articles {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Todos los Artículos</h1>
        <p>Verificación de que se extraen TODOS los 8 artículos esperados del PDF.</p>
        
        <div class="success">
            <strong>Artículos Esperados (8 total):</strong><br>
            <div class="expected-articles">
1. OMB8HS-10 - BUJIAS NGK 13/16 Fuera de Borda - 20.00 - 36.14 - 722.80
2. OMCPR8EA-9 - BUJIA - 20.00 - 143.51 - 2,870.20
3. OMCR7HIX - BUJIA - 10.00 - 144.90 - 1,449.00
4. OMCR7HSA - BUJIA NGK 5/8 (Moto) - 130.00 - 41.31 - 5,370.30
5. OMCR8E - BUJIA NGK 5/8 (Moto) - 10.00 - 92.62 - 926.20
6. OMCR8EIX - BUJIA IRIDIUM DE MOTO - 25.00 - 144.54 - 3,613.50
7. OMD8EA - BUJIA NGK 5/8 (Moto) - 125.00 - 36.04 - 4,505.00
8. OMDR8EIX - BUJIA - 30.00 - 143.10 - 4,293.00
            </div>
        </div>
        
        <div class="info">
            <strong>Mejoras Implementadas:</strong><br>
            ✅ Detección de fin de tabla más específica<br>
            ✅ No se detiene prematuramente en "IVA Total:"<br>
            ✅ Procesa todas las líneas de artículos<br>
            ✅ Patrones optimizados para códigos mixtos
        </div>
        
        <button class="btn btn-primary" onclick="testTodosArticulos()">🚀 Probar Extracción Completa</button>
        <button class="btn btn-info" onclick="mostrarLogs()">📋 Ver Logs</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Test</h2>
        <div id="resultadoTest" class="result">Haz clic en "Probar Extracción Completa" para comenzar...</div>
    </div>

    <div class="container">
        <h2>🔍 Logs del Procesamiento</h2>
        <div id="logsDetallados" class="result">Los logs aparecerán aquí...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    <script src="js/pdf-exporter.js"></script>
    
    <script>
        // Capturar logs de consola
        const originalLog = console.log;
        const originalError = console.error;
        let consoleLogs = [];

        console.log = function(...args) {
            consoleLogs.push(`${args.join(' ')}`);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleLogs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        async function testTodosArticulos() {
            let results = '=== TEST EXTRACCIÓN COMPLETA DE ARTÍCULOS ===\n\n';
            consoleLogs = []; // Limpiar logs
            
            try {
                results += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                if (!response.ok) {
                    throw new Error(`Error cargando PDF: ${response.status}`);
                }
                
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                results += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n`;
                document.getElementById('resultadoTest').textContent = results;
                
                // Procesar con detección de fin de tabla mejorada
                results += '🔄 Procesando con detección de fin de tabla mejorada...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Análisis de resultados
                results += '=== RESULTADOS ===\n';
                results += `Error: ${result.error || 'Ninguno'}\n`;
                results += `Archivo: ${result.fileName}\n`;
                results += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    results += '=== DATOS EXTRAÍDOS ===\n';
                    results += `Cliente: "${data.nombre || 'N/A'}"\n`;
                    results += `Fecha: "${data.fecha || 'N/A'}"\n`;
                    results += `OrdenRepuestos: "${data.ordenRepuestos || 'N/A'}"\n`;
                    results += `Artículos: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        results += '=== ARTÍCULOS EXTRAÍDOS ===\n';
                        
                        data.articulos.forEach((art, i) => {
                            results += `${i+1}. "${art.articulo}" - ${art.descripcion} - Cant:${art.cantidad} - Precio:${art.precioUni} - Total:${art.total}\n`;
                        });
                        
                        // Verificar completitud
                        results += '\n=== VERIFICACIÓN DE COMPLETITUD ===\n';
                        const expectedCodes = ['OMB8HS-10', 'OMCPR8EA-9', 'OMCR7HIX', 'OMCR7HSA', 'OMCR8E', 'OMCR8EIX', 'OMD8EA', 'OMDR8EIX'];
                        const extractedCodes = data.articulos.map(art => art.articulo);
                        
                        let foundCount = 0;
                        expectedCodes.forEach(code => {
                            const found = extractedCodes.includes(code);
                            results += `${found ? '✅' : '❌'} ${code}: ${found ? 'ENCONTRADO' : 'NO ENCONTRADO'}\n`;
                            if (found) foundCount++;
                        });
                        
                        results += `\n📊 RESUMEN: ${foundCount}/${expectedCodes.length} códigos encontrados\n`;
                        results += `📊 TOTAL ARTÍCULOS: ${data.articulos.length}\n\n`;
                        
                        // Test de detección de códigos
                        results += '=== TEST BOTÓN PDF ===\n';
                        const pdfExporter = new PDFExporter();
                        const hasArticleCodes = pdfExporter.hasArticleCodes([result]);
                        
                        results += `hasArticleCodes: ${hasArticleCodes}\n`;
                        results += `Botón PDF debería aparecer: ${hasArticleCodes ? 'SÍ ✅' : 'NO ❌'}\n\n`;
                        
                        // Contar códigos
                        const codigosEncontrados = data.articulos.filter(art => art.articulo && art.articulo.trim() !== '').length;
                        results += `📊 CÓDIGOS: ${codigosEncontrados}/${data.articulos.length} artículos tienen código\n\n`;
                        
                        results += '=== ESTADO FINAL ===\n';
                        if (data.articulos.length >= 8) {
                            results += '✅ ÉXITO TOTAL: Se extraen todos los artículos esperados (8+)\n';
                        } else {
                            results += `⚠️ PARCIAL: Se extraen ${data.articulos.length}/8 artículos\n`;
                        }
                        
                        if (foundCount >= 8) {
                            results += '✅ ÉXITO TOTAL: Se extraen todos los códigos esperados (8/8)\n';
                        } else {
                            results += `⚠️ PARCIAL: Se extraen ${foundCount}/8 códigos esperados\n`;
                        }
                        
                        if (hasArticleCodes) {
                            results += '✅ ÉXITO: El botón "Exportar PDF Final" debería aparecer\n';
                        }
                        
                        if (data.articulos.length >= 8 && foundCount >= 8) {
                            results += '\n🎉 ¡ÉXITO COMPLETO! TODOS LOS ARTÍCULOS EXTRAÍDOS CORRECTAMENTE\n';
                            results += '🎯 ¡LA APLICACIÓN DEBERÍA FUNCIONAR PERFECTAMENTE AHORA!\n';
                        } else {
                            results += '\n⚠️ EXTRACCIÓN PARCIAL - Revisar logs para diagnóstico\n';
                        }
                        
                    } else {
                        results += '❌ PROBLEMA: No se encontraron artículos\n';
                        results += '🔍 Revisar logs detallados para diagnóstico\n';
                    }
                } else {
                    results += '❌ NO SE EXTRAJERON DATOS\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoTest').textContent = results;
        }

        function mostrarLogs() {
            let logs = '=== LOGS DETALLADOS DEL PROCESAMIENTO ===\n\n';
            
            if (consoleLogs.length === 0) {
                logs += 'No hay logs disponibles. Ejecuta primero "Probar Extracción Completa".\n';
            } else {
                logs += consoleLogs.join('\n');
            }
            
            document.getElementById('logsDetallados').textContent = logs;
        }

        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testTodosArticulos, 1000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Modal - Verificación de Cierre</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Modal - Verificación de Cierre</h1>
        <p>Prueba para verificar que el modal de carga se cierre correctamente.</p>
        
        <button class="btn btn-primary" onclick="testModal()">🚀 Probar Modal</button>
        <button class="btn btn-success" onclick="testProcessing()">📄 Simular Procesamiento</button>
        <button class="btn btn-danger" onclick="forceCloseModal()">❌ Forzar Cierre</button>
    </div>

    <div class="container">
        <h2>📊 Estado del Modal</h2>
        <div id="results" class="result">Haz clic en un botón para comenzar...</div>
    </div>

    <!-- Modal de Carga (copiado del index.html) -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <h5>Procesando PDFs...</h5>
                    <p class="text-muted mb-3">Extrayendo información de las cotizaciones</p>
                    
                    <div class="progress mb-3">
                        <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    
                    <p id="progressText" class="small text-muted">Iniciando procesamiento...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let loadingModalInstance = null;
        
        function updateResults(message) {
            document.getElementById('results').textContent = message;
        }
        
        function testModal() {
            updateResults('=== TEST BÁSICO DEL MODAL ===\n\n');
            
            try {
                // Mostrar modal
                updateResults('1. Mostrando modal...\n');
                showLoadingModal(true);
                
                // Cerrar después de 3 segundos
                setTimeout(() => {
                    updateResults('2. Cerrando modal...\n');
                    showLoadingModal(false);
                    
                    setTimeout(() => {
                        updateResults('3. ✅ Test completado\n\nEl modal debería haberse cerrado correctamente.');
                    }, 1000);
                }, 3000);
                
            } catch (error) {
                updateResults('❌ Error en test: ' + error.message);
            }
        }
        
        function testProcessing() {
            updateResults('=== SIMULACIÓN DE PROCESAMIENTO ===\n\n');
            
            try {
                // Simular procesamiento completo
                showLoadingModal(true);
                updateResults('1. Modal mostrado\n2. Simulando procesamiento...\n');
                
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 20;
                    updateProgress(progress, 100, `archivo-${progress/20}.pdf`);
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        
                        setTimeout(() => {
                            showLoadingModal(false);
                            updateResults('3. ✅ Procesamiento simulado completado\n\nEl modal debería haberse cerrado.');
                        }, 1000);
                    }
                }, 500);
                
            } catch (error) {
                updateResults('❌ Error en simulación: ' + error.message);
            }
        }
        
        function forceCloseModal() {
            updateResults('=== FORZANDO CIERRE DEL MODAL ===\n\n');
            
            const modal = document.getElementById('loadingModal');
            
            // Método 1: Usar instancia si existe
            if (loadingModalInstance) {
                loadingModalInstance.hide();
                loadingModalInstance.dispose();
                loadingModalInstance = null;
            }
            
            // Método 2: Forzar cierre manual
            modal.classList.remove('show');
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');
            
            // Método 3: Remover backdrop
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            updateResults('✅ Modal cerrado forzadamente\n\nSe aplicaron todos los métodos de cierre.');
        }
        
        function showLoadingModal(show) {
            const modal = document.getElementById('loadingModal');
            
            if (show) {
                // Crear nueva instancia solo si no existe
                if (!loadingModalInstance) {
                    loadingModalInstance = new bootstrap.Modal(modal, {
                        backdrop: 'static',
                        keyboard: false
                    });
                }
                loadingModalInstance.show();
            } else {
                // Cerrar modal existente
                if (loadingModalInstance) {
                    loadingModalInstance.hide();
                    // Limpiar instancia después de un delay
                    setTimeout(() => {
                        if (loadingModalInstance) {
                            loadingModalInstance.dispose();
                            loadingModalInstance = null;
                        }
                    }, 500);
                }
                
                // Fallback: forzar cierre manual
                modal.classList.remove('show');
                modal.style.display = 'none';
                document.body.classList.remove('modal-open');
                
                // Remover backdrop si existe
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
        }
        
        function updateProgress(current, total, fileName) {
            const percentage = Math.round((current / total) * 100);
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
                progressBar.textContent = `${percentage}%`;
            }
            
            if (progressText) {
                progressText.textContent = `Procesando: ${fileName} (${current}/${total})`;
            }
        }
        
        // Detectar eventos del modal
        document.getElementById('loadingModal').addEventListener('shown.bs.modal', function () {
            console.log('Modal mostrado');
        });
        
        document.getElementById('loadingModal').addEventListener('hidden.bs.modal', function () {
            console.log('Modal ocultado');
        });
    </script>
</body>
</html>

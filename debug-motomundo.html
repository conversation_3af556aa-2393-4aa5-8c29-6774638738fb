<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug MOTOMUNDO - Diagnóstico Completo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 10px;
            border: 1px solid #dee2e6;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug MOTOMUNDO - Diagnóstico Completo</h1>
        <p>Diagnóstico detallado del archivo <strong>codigos.pdf</strong> para identificar por qué no se extraen los artículos.</p>
        
        <div class="warning">
            <strong>Problema Identificado:</strong><br>
            ✅ Cliente: MOTOMUNDO S.A. (detectado correctamente)<br>
            ❌ Artículos: 0 (no se están extrayendo)<br>
            🔍 Necesitamos ver el texto completo del PDF para diagnosticar
        </div>
        
        <button class="btn btn-primary" onclick="debugCompleto()">🔍 Diagnóstico Completo</button>
        <button class="btn btn-secondary" onclick="mostrarTextoCompleto()">📄 Mostrar Texto Completo</button>
        <button class="btn btn-info" onclick="analizarEstructura()">📊 Analizar Estructura</button>
    </div>

    <div class="container">
        <h2>📊 Texto Completo del PDF</h2>
        <div id="textoCompleto" class="result">Haz clic en "Mostrar Texto Completo" para ver el contenido...</div>
    </div>

    <div class="container">
        <h2>🔍 Análisis de Estructura</h2>
        <div id="analisisEstructura" class="result">Haz clic en "Analizar Estructura" para ver el análisis...</div>
    </div>

    <div class="container">
        <h2>🛠️ Diagnóstico de Procesamiento</h2>
        <div id="diagnosticoProcesamiento" class="result">Haz clic en "Diagnóstico Completo" para comenzar...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        let textoCompletoPDF = '';
        let resultadoProcesamiento = null;

        async function debugCompleto() {
            let debug = '=== DIAGNÓSTICO COMPLETO DE CODIGOS.PDF ===\n\n';
            
            try {
                debug += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('diagnosticoProcesamiento').textContent = debug;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                debug += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n\n`;
                
                // Procesar con el procesador mejorado
                debug += '🔄 Procesando con ImprovedPDFProcessor...\n';
                document.getElementById('diagnosticoProcesamiento').textContent = debug;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                resultadoProcesamiento = result;
                
                debug += '✅ Procesamiento completado\n\n';
                
                // Análisis detallado
                debug += '=== ANÁLISIS DETALLADO ===\n';
                debug += `Error: ${result.error || 'Ninguno'}\n`;
                debug += `Archivo: ${result.fileName}\n`;
                debug += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    debug += '=== DATOS EXTRAÍDOS ===\n';
                    debug += `Cliente: "${data.nombre || 'N/A'}"\n`;
                    debug += `Fecha: "${data.fecha || 'N/A'}"\n`;
                    debug += `OrdenRepuestos: "${data.ordenRepuestos || 'N/A'}"\n`;
                    debug += `Artículos: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        debug += '=== ARTÍCULOS ENCONTRADOS ===\n';
                        data.articulos.forEach((art, i) => {
                            debug += `${i+1}. Cantidad: ${art.cantidad}\n`;
                            debug += `   Código: "${art.articulo}"\n`;
                            debug += `   Descripción: "${art.descripcion}"\n`;
                            debug += `   T.Facturación: ${art.tFacturacion}\n`;
                            debug += `   PrecioUni: ${art.precioUni}\n`;
                            debug += `   Total: ${art.total}\n\n`;
                        });
                    } else {
                        debug += '❌ NO SE ENCONTRARON ARTÍCULOS\n';
                        debug += '🔍 Esto indica que el patrón de extracción no coincide\n\n';
                    }
                } else {
                    debug += '❌ NO SE EXTRAJERON DATOS\n';
                }
                
                // Obtener texto completo para análisis
                debug += '🔄 Extrayendo texto completo...\n';
                textoCompletoPDF = await extraerTextoCompleto(file);
                debug += `✅ Texto extraído: ${textoCompletoPDF.length} caracteres\n\n`;
                
                // Análisis de patrones
                debug += '=== ANÁLISIS DE PATRONES ===\n';
                debug += analizarPatrones(textoCompletoPDF);
                
            } catch (error) {
                debug += `❌ Error: ${error.message}\n`;
                console.error('Error en debug:', error);
            }
            
            document.getElementById('diagnosticoProcesamiento').textContent = debug;
        }

        async function mostrarTextoCompleto() {
            if (!textoCompletoPDF) {
                try {
                    const response = await fetch('codigos.pdf');
                    const blob = await response.blob();
                    const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                    textoCompletoPDF = await extraerTextoCompleto(file);
                } catch (error) {
                    document.getElementById('textoCompleto').textContent = `Error: ${error.message}`;
                    return;
                }
            }
            
            let texto = '=== TEXTO COMPLETO DEL PDF ===\n\n';
            texto += textoCompletoPDF;
            texto += '\n\n=== FIN DEL TEXTO ===';
            
            document.getElementById('textoCompleto').textContent = texto;
        }

        function analizarEstructura() {
            if (!textoCompletoPDF) {
                document.getElementById('analisisEstructura').textContent = 'Primero ejecuta "Mostrar Texto Completo"';
                return;
            }
            
            let analisis = '=== ANÁLISIS DE ESTRUCTURA ===\n\n';
            
            const lineas = textoCompletoPDF.split('\n');
            analisis += `Total de líneas: ${lineas.length}\n\n`;
            
            // Buscar patrones de tabla
            analisis += '=== BÚSQUEDA DE PATRONES DE TABLA ===\n';
            
            const patronesTabla = [
                'Cant', 'Cantidad', 'Articulo', 'Descripcion', 'Precio', 'Total',
                'Art', 'Cod', 'Ref', 'PrecioUni', 'DescUni'
            ];
            
            lineas.forEach((linea, i) => {
                const lineaLower = linea.toLowerCase();
                for (const patron of patronesTabla) {
                    if (lineaLower.includes(patron.toLowerCase())) {
                        analisis += `Línea ${i+1}: "${linea}" → Contiene "${patron}"\n`;
                    }
                }
            });
            
            // Buscar números que parezcan precios
            analisis += '\n=== BÚSQUEDA DE PRECIOS ===\n';
            const patronPrecio = /\d+[.,]\d{2}/g;
            lineas.forEach((linea, i) => {
                const precios = linea.match(patronPrecio);
                if (precios && precios.length > 0) {
                    analisis += `Línea ${i+1}: "${linea}" → Precios: ${precios.join(', ')}\n`;
                }
            });
            
            // Buscar líneas con múltiples números (posibles filas de tabla)
            analisis += '\n=== POSIBLES FILAS DE TABLA ===\n';
            lineas.forEach((linea, i) => {
                const numeros = linea.match(/\d+/g);
                if (numeros && numeros.length >= 3) {
                    analisis += `Línea ${i+1}: "${linea}" → ${numeros.length} números\n`;
                }
            });
            
            document.getElementById('analisisEstructura').textContent = analisis;
        }

        function analizarPatrones(texto) {
            let analisis = '';
            
            // Verificar si hay columna de artículo
            const tieneColumnaArticulo = texto.toLowerCase().includes('articulo') || 
                                       texto.toLowerCase().includes('codigo') ||
                                       texto.toLowerCase().includes('art') ||
                                       texto.toLowerCase().includes('cod');
            
            analisis += `Tiene columna de artículo: ${tieneColumnaArticulo ? 'SÍ' : 'NO'}\n`;
            
            // Contar líneas con números
            const lineas = texto.split('\n');
            const lineasConNumeros = lineas.filter(linea => /\d/.test(linea)).length;
            analisis += `Líneas con números: ${lineasConNumeros}/${lineas.length}\n`;
            
            // Buscar patrones de tabla
            const patronesEncontrados = [];
            if (texto.includes('Cant')) patronesEncontrados.push('Cant');
            if (texto.includes('Descripcion')) patronesEncontrados.push('Descripcion');
            if (texto.includes('Precio')) patronesEncontrados.push('Precio');
            if (texto.includes('Total')) patronesEncontrados.push('Total');
            
            analisis += `Patrones de tabla encontrados: ${patronesEncontrados.join(', ')}\n`;
            
            return analisis;
        }

        async function extraerTextoCompleto(file) {
            const arrayBuffer = await file.arrayBuffer();
            const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
            
            let textoCompleto = '';
            
            for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();
                
                textoCompleto += `\n=== PÁGINA ${pageNum} ===\n`;
                textContent.items.forEach(item => {
                    textoCompleto += item.str + '\n';
                });
            }
            
            return textoCompleto;
        }

        // Ejecutar diagnóstico automáticamente
        window.addEventListener('load', () => {
            setTimeout(debugCompleto, 1000);
        });
    </script>
</body>
</html>

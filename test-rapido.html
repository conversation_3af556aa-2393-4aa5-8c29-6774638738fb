<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Test Rápido - Verificación de Patrones</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Test Rápido - Verificación de Patrones</h1>
        <p>Prueba rápida para verificar que los patrones actualizados funcionan correctamente.</p>
        
        <button onclick="testPatterns()">🔍 Probar Patrones</button>
        <button onclick="testFullPDF()">📄 Probar PDF Completo</button>
    </div>

    <div class="container">
        <h2>📊 Resultados</h2>
        <div id="results" class="result">Haz clic en un botón para comenzar...</div>
    </div>

    <!-- Cargar procesador -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        // Texto real extraído del log
        const textoReal = `0000071551
 Res.El Trapiche, calle ppal,Frente Edif. Admin. d la ENEE,tgu Direccion:
99964312 Telefono:
Sucursal:
 AutoExcel S.A. de C.V
 Cotizacion de Repuestos
 TR
 OrdenRepuestos:
 Cotización:
 Repuestos y Servicios Expres El Trapiche
 250003367
 HNE02 Empresa:
 09/01/2025 Fecha:
 RES. EL TRAPICHE, CONTIGUO A IGLESIA CCI, CALLE PRINCIPAL   ,0801,HND Direccion:
 94363021 Telefono:
Nombre:   ECONO RENT A CAR S. DE R.L
 PrecioUni T. Facturacion   Total Descripcion Articulo Cant.   Procedencia   DescUni`;

        function testPatterns() {
            let results = '=== PRUEBA DE PATRONES CON TEXTO REAL ===\n\n';
            
            try {
                const processor = new ImprovedPDFProcessor();
                
                // Test Nombre
                const nombre = processor.extractNombre(textoReal);
                results += `✅ Nombre: "${nombre}"\n`;
                
                // Test Fecha
                const fecha = processor.extractFecha(textoReal);
                results += `✅ Fecha: "${fecha}"\n`;
                
                // Test OrdenRepuestos
                const orden = processor.extractOrdenRepuestos(textoReal);
                results += `✅ OrdenRepuestos: "${orden}"\n`;
                
                // Test Cotización
                const cotizacion = processor.extractCotizacion(textoReal);
                results += `✅ Cotización: "${cotizacion}"\n`;
                
                // Test Totales
                const totales = processor.extractTotales(textoReal);
                results += `✅ Totales: ${JSON.stringify(totales, null, 2)}\n`;
                
                results += '\n=== ANÁLISIS DETALLADO ===\n';
                
                // Verificar patrones específicos
                results += '\n🔍 VERIFICACIÓN DE PATRONES:\n';
                
                // OrdenRepuestos
                const ordenMatch1 = textoReal.match(/OrdenRepuestos:[\s\S]*?(\d{9})/i);
                const ordenMatch2 = textoReal.match(/(250003367)/);
                results += `OrdenRepuestos patrón 1: ${ordenMatch1 ? ordenMatch1[1] : 'No encontrado'}\n`;
                results += `OrdenRepuestos patrón 2: ${ordenMatch2 ? ordenMatch2[1] : 'No encontrado'}\n`;
                
                // Cotización
                const cotMatch1 = textoReal.match(/^(\d{10})/);
                const cotMatch2 = textoReal.match(/(0000071551)/);
                results += `Cotización patrón 1: ${cotMatch1 ? cotMatch1[1] : 'No encontrado'}\n`;
                results += `Cotización patrón 2: ${cotMatch2 ? cotMatch2[1] : 'No encontrado'}\n`;
                
                // Fecha
                const fechaMatch = textoReal.match(/Fecha:\s*(\d{1,2}\/\d{1,2}\/\d{4})/i);
                results += `Fecha patrón: ${fechaMatch ? fechaMatch[1] : 'No encontrado'}\n`;
                
                // Nombre
                const nombreMatch = textoReal.match(/Nombre:\s*([^\n\r]+)/i);
                results += `Nombre patrón: ${nombreMatch ? nombreMatch[1].trim() : 'No encontrado'}\n`;
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('results').textContent = results;
        }
        
        async function testFullPDF() {
            let results = '=== PRUEBA CON PDF COMPLETO ===\n\n';
            
            try {
                results += '🔄 Cargando FORMATO.pdf...\n';
                
                const response = await fetch('FORMATO.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'FORMATO.pdf', { type: 'application/pdf' });
                
                results += '✅ PDF cargado, procesando...\n';
                document.getElementById('results').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                results += '=== DATOS EXTRAÍDOS ===\n';
                results += `Nombre: "${result.extractedData.nombre}"\n`;
                results += `Fecha: "${result.extractedData.fecha}"\n`;
                results += `OrdenRepuestos: "${result.extractedData.ordenRepuestos}"\n`;
                results += `Cotización: "${result.extractedData.cotizacion}"\n`;
                results += `Artículos: ${result.extractedData.articulos.length}\n`;
                
                if (result.extractedData.articulos.length > 0) {
                    results += '\n=== ARTÍCULOS ===\n';
                    result.extractedData.articulos.forEach((art, i) => {
                        results += `${i+1}. ${art.descripcion} - Cant: ${art.cantidad} - $${art.total}\n`;
                    });
                }
                
                results += '\n=== TOTALES ===\n';
                results += `SubTotal: $${result.extractedData.totales.subtotal || 'N/A'}\n`;
                results += `Descuento: $${result.extractedData.totales.descuento || 'N/A'}\n`;
                results += `Venta Neta: $${result.extractedData.totales.ventaNeta || 'N/A'}\n`;
                results += `IVA: $${result.extractedData.totales.iva || 'N/A'}\n`;
                results += `Total: $${result.extractedData.totales.total || 'N/A'}\n`;
                
                results += `\n=== INFO TÉCNICA ===\n`;
                results += `Tiempo de procesamiento: ${result.processingTime}ms\n`;
                results += `Caracteres extraídos: ${result.rawText.length}\n`;
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test completo:', error);
            }
            
            document.getElementById('results').textContent = results;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testPatterns, 1000);
        });
    </script>
</body>
</html>

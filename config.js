/**
 * Archivo de Configuración - Dashboard de Cotizaciones
 * Personaliza patrones de extracción y comportamiento de la aplicación
 */

window.CotizacionesConfig = {
    
    // ===== CONFIGURACIÓN DE EXTRACCIÓN =====
    
    /**
     * Patrones personalizados para extracción de datos
     * Puedes agregar o modificar estos patrones según tus necesidades
     */
    extractionPatterns: {
        
        // Patrones para detectar nombres de clientes
        cliente: [
            /(?:cliente|client|empresa|company)[\s:]+([^\n\r]+)/i,
            /(?:para|to|destinatario|dirigido\s+a)[\s:]+([^\n\r]+)/i,
            /(?:señor|sr|sra|señora|mr|mrs)[\s\.]+([^\n\r]+)/i,
            /(?:razón\s+social|business\s+name)[\s:]+([^\n\r]+)/i,
            /(?:facturar\s+a|bill\s+to)[\s:]+([^\n\r]+)/i
        ],
        
        // Patrones para detectar fechas
        fecha: [
            /(?:fecha|date|cotización\s+del)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
            /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/g,
            /(?:cotización|quote|presupuesto)[\s\w]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
            /(?:válida\s+hasta|valid\s+until)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i
        ],
        
        // Patrones para números de cotización
        numeroCotizacion: [
            /(?:cotización|quote|cot|no|número|#|folio)[\s\.:]*([A-Z0-9\-]+)/i,
            /(?:ref|referencia|reference)[\s\.:]*([A-Z0-9\-]+)/i,
            /(?:presupuesto|budget)[\s\.:]*([A-Z0-9\-]+)/i
        ],
        
        // Patrones para detectar totales
        totales: {
            total: /(?:total|grand\s+total|importe\s+total)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i,
            subtotal: /(?:subtotal|sub\s+total|importe\s+neto)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i,
            iva: /(?:iva|tax|impuesto|vat)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i,
            descuento: /(?:descuento|discount|rebaja)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i
        }
    },
    
    // ===== CONFIGURACIÓN DE PROCESAMIENTO =====
    
    processing: {
        // Tamaño máximo de archivo en bytes (10MB por defecto)
        maxFileSize: 10 * 1024 * 1024,
        
        // Número máximo de archivos a procesar simultáneamente
        maxConcurrentFiles: 5,
        
        // Tiempo máximo de procesamiento por archivo en milisegundos
        processingTimeout: 30000,
        
        // Mínima longitud de descripción de artículo para ser considerada válida
        minDescriptionLength: 3,
        
        // Precio mínimo para considerar un artículo válido
        minPrice: 0.01,
        
        // Máximo número de artículos a extraer por PDF
        maxItemsPerPdf: 1000
    },
    
    // ===== CONFIGURACIÓN DE INTERFAZ =====
    
    ui: {
        // Idioma de la interfaz
        language: 'es',
        
        // Formato de moneda
        currency: {
            symbol: '$',
            locale: 'es-MX',
            decimals: 2
        },
        
        // Formato de fecha
        dateFormat: {
            locale: 'es-MX',
            options: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }
        },
        
        // Número máximo de elementos a mostrar en tablas
        maxTableRows: 100,
        
        // Tiempo de auto-ocultado de alertas en milisegundos
        alertTimeout: 5000
    },
    
    // ===== CONFIGURACIÓN DE EXPORTACIÓN =====
    
    export: {
        // Nombre por defecto del archivo Excel
        defaultFileName: 'reporte-cotizaciones',
        
        // Incluir fecha en el nombre del archivo
        includeDateInFileName: true,
        
        // Hojas a incluir en el Excel
        sheets: {
            resumen: true,
            cotizaciones: true,
            articulos: true,
            clientes: true,
            estadisticas: true
        },
        
        // Configuración de columnas para cada hoja
        columnWidths: {
            resumen: [30, 15, 15, 20],
            cotizaciones: [25, 25, 12, 15, 10, 12, 10, 12, 20],
            articulos: [25, 25, 12, 10, 40, 15, 15],
            clientes: [30, 12, 15, 15, 20, 15],
            estadisticas: [30, 15, 20]
        }
    },
    
    // ===== CONFIGURACIÓN DE VALIDACIÓN =====
    
    validation: {
        // Palabras clave que indican que una línea contiene un artículo
        itemKeywords: [
            'cantidad', 'descripción', 'precio', 'total',
            'qty', 'description', 'price', 'amount',
            'cant', 'desc', 'importe'
        ],
        
        // Palabras que se deben ignorar al extraer descripciones
        ignoreWords: [
            'subtotal', 'total', 'iva', 'descuento',
            'página', 'page', 'hoja', 'sheet',
            'cotización', 'quote', 'presupuesto'
        ],
        
        // Patrones que indican líneas de encabezado (ignorar)
        headerPatterns: [
            /^(cantidad|qty|cant)[\s\|]/i,
            /^(descripción|description|desc)[\s\|]/i,
            /^(precio|price)[\s\|]/i,
            /^(total|amount|importe)[\s\|]/i
        ]
    },
    
    // ===== CONFIGURACIÓN DE DEPURACIÓN =====
    
    debug: {
        // Habilitar logs detallados en consola
        enableLogging: false,
        
        // Mostrar texto extraído de PDFs en consola
        showExtractedText: false,
        
        // Mostrar patrones que coinciden
        showMatchedPatterns: false,
        
        // Guardar datos de depuración en localStorage
        saveDebugData: false
    },
    
    // ===== MÉTODOS DE UTILIDAD =====
    
    /**
     * Obtener configuración específica
     * @param {string} path - Ruta de la configuración (ej: 'ui.currency.symbol')
     * @returns {any} Valor de la configuración
     */
    get(path) {
        return path.split('.').reduce((obj, key) => obj && obj[key], this);
    },
    
    /**
     * Establecer configuración específica
     * @param {string} path - Ruta de la configuración
     * @param {any} value - Nuevo valor
     */
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, this);
        target[lastKey] = value;
    },
    
    /**
     * Cargar configuración desde localStorage
     */
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('cotizaciones-config');
            if (saved) {
                const config = JSON.parse(saved);
                Object.assign(this, config);
            }
        } catch (error) {
            console.warn('No se pudo cargar configuración guardada:', error);
        }
    },
    
    /**
     * Guardar configuración en localStorage
     */
    saveToStorage() {
        try {
            localStorage.setItem('cotizaciones-config', JSON.stringify(this));
        } catch (error) {
            console.warn('No se pudo guardar configuración:', error);
        }
    },
    
    /**
     * Restaurar configuración por defecto
     */
    reset() {
        // Recargar la página para restaurar configuración original
        if (confirm('¿Estás seguro de que quieres restaurar la configuración por defecto?')) {
            localStorage.removeItem('cotizaciones-config');
            location.reload();
        }
    }
};

// ===== INICIALIZACIÓN =====

// Cargar configuración guardada al iniciar
document.addEventListener('DOMContentLoaded', () => {
    window.CotizacionesConfig.loadFromStorage();
    
    // Aplicar configuración de depuración
    if (window.CotizacionesConfig.debug.enableLogging) {
        console.log('Dashboard de Cotizaciones - Modo depuración activado');
        console.log('Configuración actual:', window.CotizacionesConfig);
    }
});

// ===== EJEMPLOS DE USO =====

/*
// Cambiar símbolo de moneda
CotizacionesConfig.set('ui.currency.symbol', '€');

// Habilitar depuración
CotizacionesConfig.set('debug.enableLogging', true);

// Agregar nuevo patrón de cliente
CotizacionesConfig.extractionPatterns.cliente.push(/mi-patron-personalizado/i);

// Guardar cambios
CotizacionesConfig.saveToStorage();

// Obtener configuración
const maxFileSize = CotizacionesConfig.get('processing.maxFileSize');
*/

/**
 * Excel Exporter Module
 * Handles Excel file generation and download using SheetJS
 */

class ExcelExporter {
    constructor() {
        this.XLSX = window.XLSX;
    }

    /**
     * Export processed data to Excel file
     * @param {Array} processedData - Array of processed PDF data
     * @param {string} fileName - Output file name
     */
    exportToExcel(processedData, fileName = 'reporte-cotizaciones.xlsx') {
        try {
            // Create workbook
            const workbook = this.XLSX.utils.book_new();
            
            // Create different sheets
            this.addResumenSheet(workbook, processedData);
            this.addCotizacionesSheet(workbook, processedData);
            this.addArticulosSheet(workbook, processedData);
            this.addClientesSheet(workbook, processedData);
            this.addEstadisticasSheet(workbook, processedData);
            
            // Generate and download file
            this.XLSX.writeFile(workbook, fileName);
            
            return true;
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            throw new Error('Error al generar archivo Excel: ' + error.message);
        }
    }

    /**
     * Add summary sheet
     * @param {Object} workbook 
     * @param {Array} data 
     */
    addResumenSheet(workbook, data) {
        const validData = data.filter(item => !item.error);
        const totalCotizaciones = validData.length;
        const totalClientes = new Set(validData.map(item => item.extractedData?.cliente)).size;
        const totalArticulos = validData.reduce((sum, item) => 
            sum + (item.extractedData?.articulos?.length || 0), 0);
        const totalMonto = validData.reduce((sum, item) => 
            sum + (item.extractedData?.totales?.total || 0), 0);

        const resumenData = [
            ['REPORTE DE COTIZACIONES - RESUMEN EJECUTIVO'],
            [''],
            ['Fecha de generación:', new Date().toLocaleDateString('es-ES')],
            ['Hora de generación:', new Date().toLocaleTimeString('es-ES')],
            [''],
            ['ESTADÍSTICAS GENERALES'],
            ['Total de PDFs procesados:', data.length],
            ['PDFs procesados exitosamente:', validData.length],
            ['PDFs con errores:', data.length - validData.length],
            ['Total de cotizaciones:', totalCotizaciones],
            ['Total de clientes únicos:', totalClientes],
            ['Total de artículos:', totalArticulos],
            ['Monto total estimado:', `$${totalMonto.toLocaleString('es-ES', {minimumFractionDigits: 2})}`],
            [''],
            ['DISTRIBUCIÓN POR CLIENTE'],
        ];

        // Add client distribution
        const clienteStats = this.getClienteStats(validData);
        clienteStats.forEach(stat => {
            resumenData.push([
                stat.cliente,
                stat.cotizaciones,
                stat.articulos,
                `$${stat.total.toLocaleString('es-ES', {minimumFractionDigits: 2})}`
            ]);
        });

        const worksheet = this.XLSX.utils.aoa_to_sheet(resumenData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 30 },
            { width: 15 },
            { width: 15 },
            { width: 20 }
        ];

        // Add styling
        this.addSheetStyling(worksheet, resumenData.length);
        
        this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Resumen');
    }

    /**
     * Add cotizaciones sheet
     * @param {Object} workbook 
     * @param {Array} data 
     */
    addCotizacionesSheet(workbook, data) {
        const validData = data.filter(item => !item.error);
        
        const cotizacionesData = [
            ['ARCHIVO', 'NOMBRE', 'FECHA', 'ORDEN REPUESTOS', 'COTIZACIÓN', 'ARTÍCULOS', 'SUBTOTAL', 'DESCUENTO', 'VENTA NETA', 'IVA', 'TOTAL', 'ESTADO']
        ];

        validData.forEach(item => {
            const extracted = item.extractedData || {};
            cotizacionesData.push([
                item.fileName,
                extracted.nombre || 'N/A',
                extracted.fecha || 'N/A',
                extracted.ordenRepuestos || 'N/A',
                extracted.cotizacion || 'N/A',
                extracted.articulos?.length || 0,
                extracted.totales?.subtotal || 0,
                extracted.totales?.descuento || 0,
                extracted.totales?.ventaNeta || 0,
                extracted.totales?.iva || 0,
                extracted.totales?.total || 0,
                'Procesado'
            ]);
        });

        // Add error files
        data.filter(item => item.error).forEach(item => {
            cotizacionesData.push([
                item.fileName,
                'ERROR',
                'N/A',
                'N/A',
                0,
                0,
                0,
                0,
                'Error: ' + item.error
            ]);
        });

        const worksheet = this.XLSX.utils.aoa_to_sheet(cotizacionesData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 25 }, // Archivo
            { width: 25 }, // Cliente
            { width: 12 }, // Fecha
            { width: 15 }, // No. Cotización
            { width: 10 }, // Artículos
            { width: 12 }, // Subtotal
            { width: 10 }, // IVA
            { width: 12 }, // Total
            { width: 20 }  // Estado
        ];

        this.addSheetStyling(worksheet, cotizacionesData.length);
        this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Cotizaciones');
    }

    /**
     * Add articles sheet
     * @param {Object} workbook 
     * @param {Array} data 
     */
    addArticulosSheet(workbook, data) {
        const validData = data.filter(item => !item.error);
        
        const articulosData = [
            ['ARCHIVO', 'NOMBRE', 'FECHA', 'CANTIDAD', 'ARTÍCULO', 'DESCRIPCIÓN', 'T.FACTURACIÓN', 'PROCEDENCIA', 'PRECIO UNI', 'DESC UNI', 'TOTAL']
        ];

        validData.forEach(item => {
            const extracted = item.extractedData || {};
            const articulos = extracted.articulos || [];

            if (articulos.length === 0) {
                articulosData.push([
                    item.fileName,
                    extracted.nombre || 'N/A',
                    extracted.fecha || 'N/A',
                    0,
                    'N/A',
                    'Sin artículos detectados',
                    'N/A',
                    'N/A',
                    0,
                    0,
                    0
                ]);
            } else {
                articulos.forEach(articulo => {
                    articulosData.push([
                        item.fileName,
                        extracted.nombre || 'N/A',
                        extracted.fecha || 'N/A',
                        articulo.cantidad || 1,
                        articulo.articulo || 'N/A',
                        articulo.descripcion || 'N/A',
                        articulo.tFacturacion || 'N/A',
                        articulo.procedencia || 'N/A',
                        articulo.precioUni || 0,
                        articulo.descUni || 0,
                        articulo.total || 0
                    ]);
                });
            }
        });

        const worksheet = this.XLSX.utils.aoa_to_sheet(articulosData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 25 }, // Archivo
            { width: 25 }, // Cliente
            { width: 12 }, // Fecha
            { width: 10 }, // Cantidad
            { width: 40 }, // Descripción
            { width: 15 }, // Precio Unitario
            { width: 15 }  // Total
        ];

        this.addSheetStyling(worksheet, articulosData.length);
        this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Artículos');
    }

    /**
     * Add clients sheet
     * @param {Object} workbook 
     * @param {Array} data 
     */
    addClientesSheet(workbook, data) {
        const validData = data.filter(item => !item.error);
        const clienteStats = this.getClienteStats(validData);
        
        const clientesData = [
            ['CLIENTE', 'COTIZACIONES', 'TOTAL ARTÍCULOS', 'MONTO TOTAL', 'PROMEDIO POR COTIZACIÓN', 'ÚLTIMA COTIZACIÓN']
        ];

        clienteStats.forEach(stat => {
            clientesData.push([
                stat.cliente,
                stat.cotizaciones,
                stat.articulos,
                stat.total,
                stat.promedio,
                stat.ultimaFecha
            ]);
        });

        const worksheet = this.XLSX.utils.aoa_to_sheet(clientesData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 30 }, // Cliente
            { width: 12 }, // Cotizaciones
            { width: 15 }, // Total Artículos
            { width: 15 }, // Monto Total
            { width: 20 }, // Promedio
            { width: 15 }  // Última Cotización
        ];

        this.addSheetStyling(worksheet, clientesData.length);
        this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Clientes');
    }

    /**
     * Add statistics sheet
     * @param {Object} workbook 
     * @param {Array} data 
     */
    addEstadisticasSheet(workbook, data) {
        const validData = data.filter(item => !item.error);
        
        // Monthly statistics
        const monthlyStats = this.getMonthlyStats(validData);
        
        const estadisticasData = [
            ['ESTADÍSTICAS DETALLADAS'],
            [''],
            ['DISTRIBUCIÓN MENSUAL'],
            ['MES', 'COTIZACIONES', 'MONTO TOTAL'],
        ];

        monthlyStats.forEach(stat => {
            estadisticasData.push([stat.mes, stat.cotizaciones, stat.total]);
        });

        estadisticasData.push(['']);
        estadisticasData.push(['ARTÍCULOS MÁS COTIZADOS']);
        estadisticasData.push(['DESCRIPCIÓN', 'FRECUENCIA', 'PRECIO PROMEDIO']);

        const topArticulos = this.getTopArticulos(validData);
        topArticulos.forEach(articulo => {
            estadisticasData.push([
                articulo.descripcion,
                articulo.frecuencia,
                articulo.precioPromedio
            ]);
        });

        const worksheet = this.XLSX.utils.aoa_to_sheet(estadisticasData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 30 },
            { width: 15 },
            { width: 20 }
        ];

        this.addSheetStyling(worksheet, estadisticasData.length);
        this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Estadísticas');
    }

    /**
     * Get client statistics
     * @param {Array} data 
     * @returns {Array}
     */
    getClienteStats(data) {
        const clienteMap = new Map();

        data.forEach(item => {
            const nombre = item.extractedData?.nombre || 'Nombre no identificado';
            const total = item.extractedData?.totales?.total || 0;
            const articulos = item.extractedData?.articulos?.length || 0;
            const fecha = item.extractedData?.fecha || '';

            if (!clienteMap.has(nombre)) {
                clienteMap.set(nombre, {
                    cliente: nombre, // Mantener compatibilidad con el nombre del campo
                    cotizaciones: 0,
                    total: 0,
                    articulos: 0,
                    fechas: []
                });
            }

            const stat = clienteMap.get(nombre);
            stat.cotizaciones++;
            stat.total += total;
            stat.articulos += articulos;
            stat.fechas.push(fecha);
        });

        return Array.from(clienteMap.values()).map(stat => ({
            ...stat,
            promedio: stat.cotizaciones > 0 ? stat.total / stat.cotizaciones : 0,
            ultimaFecha: stat.fechas.sort().pop() || 'N/A'
        })).sort((a, b) => b.total - a.total);
    }

    /**
     * Get monthly statistics
     * @param {Array} data 
     * @returns {Array}
     */
    getMonthlyStats(data) {
        const monthlyMap = new Map();

        data.forEach(item => {
            const fecha = item.extractedData?.fecha || '';
            const total = item.extractedData?.totales?.total || 0;
            
            if (fecha) {
                const mes = fecha.substring(0, 7); // YYYY-MM
                
                if (!monthlyMap.has(mes)) {
                    monthlyMap.set(mes, { mes, cotizaciones: 0, total: 0 });
                }
                
                const stat = monthlyMap.get(mes);
                stat.cotizaciones++;
                stat.total += total;
            }
        });

        return Array.from(monthlyMap.values()).sort((a, b) => a.mes.localeCompare(b.mes));
    }

    /**
     * Get top articles
     * @param {Array} data 
     * @returns {Array}
     */
    getTopArticulos(data) {
        const articuloMap = new Map();

        data.forEach(item => {
            const articulos = item.extractedData?.articulos || [];
            
            articulos.forEach(articulo => {
                const desc = articulo.descripcion?.toLowerCase() || '';
                const precio = articulo.precio || 0;
                
                if (desc.length > 5) {
                    if (!articuloMap.has(desc)) {
                        articuloMap.set(desc, {
                            descripcion: articulo.descripcion,
                            frecuencia: 0,
                            precios: []
                        });
                    }
                    
                    const stat = articuloMap.get(desc);
                    stat.frecuencia++;
                    stat.precios.push(precio);
                }
            });
        });

        return Array.from(articuloMap.values())
            .map(stat => ({
                ...stat,
                precioPromedio: stat.precios.length > 0 
                    ? stat.precios.reduce((sum, p) => sum + p, 0) / stat.precios.length 
                    : 0
            }))
            .sort((a, b) => b.frecuencia - a.frecuencia)
            .slice(0, 20); // Top 20
    }

    /**
     * Add basic styling to worksheet
     * @param {Object} worksheet 
     * @param {number} rowCount 
     */
    addSheetStyling(worksheet, rowCount) {
        // This is a basic implementation
        // SheetJS Pro version would allow more advanced styling
        
        // Set header row style (row 1)
        const range = this.XLSX.utils.decode_range(worksheet['!ref']);
        
        // Auto-filter for data tables
        if (rowCount > 1) {
            worksheet['!autofilter'] = { ref: worksheet['!ref'] };
        }
    }

    /**
     * Export single sheet to CSV
     * @param {Array} data 
     * @param {string} sheetType 
     * @param {string} fileName 
     */
    exportToCSV(data, sheetType = 'cotizaciones', fileName = 'reporte.csv') {
        try {
            let csvData = [];
            
            switch (sheetType) {
                case 'cotizaciones':
                    csvData = this.prepareCotizacionesCSV(data);
                    break;
                case 'articulos':
                    csvData = this.prepareArticulosCSV(data);
                    break;
                case 'clientes':
                    csvData = this.prepareClientesCSV(data);
                    break;
                default:
                    throw new Error('Tipo de reporte no válido');
            }
            
            const worksheet = this.XLSX.utils.aoa_to_sheet(csvData);
            const workbook = this.XLSX.utils.book_new();
            this.XLSX.utils.book_append_sheet(workbook, worksheet, 'Datos');
            
            this.XLSX.writeFile(workbook, fileName);
            
            return true;
        } catch (error) {
            console.error('Error exporting to CSV:', error);
            throw new Error('Error al generar archivo CSV: ' + error.message);
        }
    }

    /**
     * Prepare cotizaciones data for CSV
     * @param {Array} data 
     * @returns {Array}
     */
    prepareCotizacionesCSV(data) {
        const validData = data.filter(item => !item.error);
        const csvData = [
            ['Archivo', 'Cliente', 'Fecha', 'No. Cotización', 'Artículos', 'Total']
        ];

        validData.forEach(item => {
            const extracted = item.extractedData || {};
            csvData.push([
                item.fileName,
                extracted.cliente || 'N/A',
                extracted.fecha || 'N/A',
                extracted.numeroCotizacion || 'N/A',
                extracted.articulos?.length || 0,
                extracted.totales?.total || 0
            ]);
        });

        return csvData;
    }

    /**
     * Prepare articles data for CSV
     * @param {Array} data 
     * @returns {Array}
     */
    prepareArticulosCSV(data) {
        const validData = data.filter(item => !item.error);
        const csvData = [
            ['Cliente', 'Fecha', 'Cantidad', 'Descripción', 'Precio', 'Total']
        ];

        validData.forEach(item => {
            const extracted = item.extractedData || {};
            const articulos = extracted.articulos || [];
            
            articulos.forEach(articulo => {
                csvData.push([
                    extracted.cliente || 'N/A',
                    extracted.fecha || 'N/A',
                    articulo.cantidad || 1,
                    articulo.descripcion || 'N/A',
                    articulo.precio || 0,
                    articulo.total || 0
                ]);
            });
        });

        return csvData;
    }

    /**
     * Prepare clients data for CSV
     * @param {Array} data 
     * @returns {Array}
     */
    prepareClientesCSV(data) {
        const validData = data.filter(item => !item.error);
        const clienteStats = this.getClienteStats(validData);
        
        const csvData = [
            ['Cliente', 'Cotizaciones', 'Total Artículos', 'Monto Total', 'Promedio']
        ];

        clienteStats.forEach(stat => {
            csvData.push([
                stat.cliente,
                stat.cotizaciones,
                stat.articulos,
                stat.total,
                stat.promedio
            ]);
        });

        return csvData;
    }
}

// Export for use in other modules
window.ExcelExporter = ExcelExporter;

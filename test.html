<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Dashboard de Cotizaciones</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Dashboard de Cotizaciones</h1>
    
    <div class="test-section">
        <h2>1. Verificación de Librerías</h2>
        <div id="libraries-test">
            <p>Verificando librerías externas...</p>
        </div>
        <button onclick="testLibraries()">Probar Librerías</button>
    </div>
    
    <div class="test-section">
        <h2>2. Test de Configuración</h2>
        <div id="config-test">
            <p>Verificando archivo de configuración...</p>
        </div>
        <button onclick="testConfig()">Probar Configuración</button>
    </div>
    
    <div class="test-section">
        <h2>3. Test de Procesador PDF</h2>
        <div id="pdf-test">
            <p>Verificando módulo de procesamiento PDF...</p>
        </div>
        <button onclick="testPDFProcessor()">Probar PDF Processor</button>
    </div>
    
    <div class="test-section">
        <h2>4. Test de Exportador Excel</h2>
        <div id="excel-test">
            <p>Verificando módulo de exportación Excel...</p>
        </div>
        <button onclick="testExcelExporter()">Probar Excel Exporter</button>
    </div>
    
    <div class="test-section">
        <h2>5. Test de Datos de Ejemplo</h2>
        <div id="data-test">
            <p>Verificando datos de ejemplo...</p>
        </div>
        <button onclick="testSampleData()">Probar Datos de Ejemplo</button>
        <button onclick="simulateProcessing()">Simular Procesamiento</button>
    </div>
    
    <div class="test-section">
        <h2>6. Test Completo</h2>
        <div id="full-test">
            <p>Ejecutar todos los tests...</p>
        </div>
        <button onclick="runAllTests()">Ejecutar Todos los Tests</button>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Cargar módulos -->
    <script src="config.js"></script>
    <script src="js/pdf-processor.js"></script>
    <script src="js/excel-exporter.js"></script>
    
    <script>
        // Funciones de test
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<p class="${className}">${message}</p>`;
        }
        
        function testLibraries() {
            const element = document.getElementById('libraries-test');
            element.innerHTML = '<h3>Resultados:</h3>';
            
            // Test PDF.js
            if (typeof window['pdfjs-dist/build/pdf'] !== 'undefined') {
                log('libraries-test', '✅ PDF.js cargado correctamente', 'success');
            } else {
                log('libraries-test', '❌ PDF.js no encontrado', 'error');
            }
            
            // Test SheetJS
            if (typeof XLSX !== 'undefined') {
                log('libraries-test', '✅ SheetJS cargado correctamente', 'success');
                log('libraries-test', `📊 Versión SheetJS: ${XLSX.version}`, 'info');
            } else {
                log('libraries-test', '❌ SheetJS no encontrado', 'error');
            }
            
            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                log('libraries-test', '✅ Bootstrap cargado correctamente', 'success');
            } else {
                log('libraries-test', '⚠️ Bootstrap no encontrado (normal en test)', 'warning');
            }
        }
        
        function testConfig() {
            const element = document.getElementById('config-test');
            element.innerHTML = '<h3>Resultados:</h3>';
            
            if (typeof CotizacionesConfig !== 'undefined') {
                log('config-test', '✅ Configuración cargada correctamente', 'success');
                log('config-test', `📋 Patrones de cliente: ${CotizacionesConfig.extractionPatterns.cliente.length}`, 'info');
                log('config-test', `📅 Patrones de fecha: ${CotizacionesConfig.extractionPatterns.fecha.length}`, 'info');
                log('config-test', `💰 Símbolo de moneda: ${CotizacionesConfig.ui.currency.symbol}`, 'info');
                
                // Test métodos
                try {
                    const symbol = CotizacionesConfig.get('ui.currency.symbol');
                    log('config-test', `✅ Método get() funciona: ${symbol}`, 'success');
                } catch (error) {
                    log('config-test', `❌ Error en método get(): ${error.message}`, 'error');
                }
            } else {
                log('config-test', '❌ Configuración no encontrada', 'error');
            }
        }
        
        function testPDFProcessor() {
            const element = document.getElementById('pdf-test');
            element.innerHTML = '<h3>Resultados:</h3>';
            
            if (typeof PDFProcessor !== 'undefined') {
                log('pdf-test', '✅ PDFProcessor clase disponible', 'success');
                
                try {
                    const processor = new PDFProcessor();
                    log('pdf-test', '✅ PDFProcessor instanciado correctamente', 'success');
                    
                    // Test métodos
                    if (typeof processor.parsePrice === 'function') {
                        const price = processor.parsePrice('$1,234.56');
                        log('pdf-test', `✅ parsePrice() funciona: $1,234.56 → ${price}`, 'success');
                    }
                    
                    if (typeof processor.normalizeDate === 'function') {
                        const date = processor.normalizeDate('15/01/2024');
                        log('pdf-test', `✅ normalizeDate() funciona: 15/01/2024 → ${date}`, 'success');
                    }
                    
                } catch (error) {
                    log('pdf-test', `❌ Error instanciando PDFProcessor: ${error.message}`, 'error');
                }
            } else {
                log('pdf-test', '❌ PDFProcessor no encontrado', 'error');
            }
        }
        
        function testExcelExporter() {
            const element = document.getElementById('excel-test');
            element.innerHTML = '<h3>Resultados:</h3>';
            
            if (typeof ExcelExporter !== 'undefined') {
                log('excel-test', '✅ ExcelExporter clase disponible', 'success');
                
                try {
                    const exporter = new ExcelExporter();
                    log('excel-test', '✅ ExcelExporter instanciado correctamente', 'success');
                    
                    // Test métodos
                    if (typeof exporter.getClienteStats === 'function') {
                        log('excel-test', '✅ Método getClienteStats() disponible', 'success');
                    }
                    
                    if (typeof exporter.exportToExcel === 'function') {
                        log('excel-test', '✅ Método exportToExcel() disponible', 'success');
                    }
                    
                } catch (error) {
                    log('excel-test', `❌ Error instanciando ExcelExporter: ${error.message}`, 'error');
                }
            } else {
                log('excel-test', '❌ ExcelExporter no encontrado', 'error');
            }
        }
        
        async function testSampleData() {
            const element = document.getElementById('data-test');
            element.innerHTML = '<h3>Resultados:</h3>';
            
            try {
                const response = await fetch('data-example.json');
                const data = await response.json();
                
                log('data-test', '✅ Datos de ejemplo cargados correctamente', 'success');
                log('data-test', `📊 Total de archivos de ejemplo: ${data.sampleData.length}`, 'info');
                log('data-test', `👥 Total de clientes únicos: ${Object.keys(data.statistics.clientDistribution).length}`, 'info');
                log('data-test', `💰 Monto total: $${data.statistics.totalAmount.toLocaleString()}`, 'info');
                
            } catch (error) {
                log('data-test', `❌ Error cargando datos de ejemplo: ${error.message}`, 'error');
            }
        }
        
        async function simulateProcessing() {
            const element = document.getElementById('data-test');
            
            try {
                const response = await fetch('data-example.json');
                const data = await response.json();
                
                log('data-test', '🔄 Simulando procesamiento con datos de ejemplo...', 'info');
                
                // Simular exportación
                if (typeof ExcelExporter !== 'undefined') {
                    const exporter = new ExcelExporter();
                    
                    // Test con datos de ejemplo
                    const clientStats = exporter.getClienteStats(data.sampleData);
                    log('data-test', `✅ Estadísticas de cliente calculadas: ${clientStats.length} clientes`, 'success');
                    
                    const monthlyStats = exporter.getMonthlyStats(data.sampleData);
                    log('data-test', `✅ Estadísticas mensuales calculadas: ${monthlyStats.length} meses`, 'success');
                    
                    log('data-test', '✅ Simulación de procesamiento completada', 'success');
                } else {
                    log('data-test', '❌ ExcelExporter no disponible para simulación', 'error');
                }
                
            } catch (error) {
                log('data-test', `❌ Error en simulación: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            const element = document.getElementById('full-test');
            element.innerHTML = '<h3>Ejecutando todos los tests...</h3>';
            
            let passed = 0;
            let total = 0;
            
            // Test 1: Librerías
            total++;
            if (typeof XLSX !== 'undefined' && typeof window['pdfjs-dist/build/pdf'] !== 'undefined') {
                passed++;
                log('full-test', '✅ Test 1: Librerías - PASADO', 'success');
            } else {
                log('full-test', '❌ Test 1: Librerías - FALLIDO', 'error');
            }
            
            // Test 2: Configuración
            total++;
            if (typeof CotizacionesConfig !== 'undefined') {
                passed++;
                log('full-test', '✅ Test 2: Configuración - PASADO', 'success');
            } else {
                log('full-test', '❌ Test 2: Configuración - FALLIDO', 'error');
            }
            
            // Test 3: Módulos
            total++;
            if (typeof PDFProcessor !== 'undefined' && typeof ExcelExporter !== 'undefined') {
                passed++;
                log('full-test', '✅ Test 3: Módulos - PASADO', 'success');
            } else {
                log('full-test', '❌ Test 3: Módulos - FALLIDO', 'error');
            }
            
            // Test 4: Datos de ejemplo
            total++;
            try {
                const response = await fetch('data-example.json');
                const data = await response.json();
                if (data.sampleData && data.sampleData.length > 0) {
                    passed++;
                    log('full-test', '✅ Test 4: Datos de ejemplo - PASADO', 'success');
                } else {
                    log('full-test', '❌ Test 4: Datos de ejemplo - FALLIDO', 'error');
                }
            } catch (error) {
                log('full-test', '❌ Test 4: Datos de ejemplo - FALLIDO', 'error');
            }
            
            // Resultado final
            const percentage = Math.round((passed / total) * 100);
            if (percentage === 100) {
                log('full-test', `🎉 TODOS LOS TESTS PASARON (${passed}/${total}) - ${percentage}%`, 'success');
                log('full-test', '✅ La aplicación está lista para usar', 'success');
            } else if (percentage >= 75) {
                log('full-test', `⚠️ MAYORÍA DE TESTS PASARON (${passed}/${total}) - ${percentage}%`, 'warning');
                log('full-test', '⚠️ La aplicación debería funcionar con limitaciones', 'warning');
            } else {
                log('full-test', `❌ MUCHOS TESTS FALLARON (${passed}/${total}) - ${percentage}%`, 'error');
                log('full-test', '❌ Revisar configuración antes de usar', 'error');
            }
        }
        
        // Ejecutar test básico al cargar
        window.addEventListener('load', () => {
            setTimeout(() => {
                testLibraries();
                testConfig();
            }, 1000);
        });
    </script>
</body>
</html>

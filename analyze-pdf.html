<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analizador de PDF - FORMATO.pdf</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .text-output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        .analysis {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .pattern-match {
            background: #fff3cd;
            padding: 5px;
            margin: 2px 0;
            border-left: 3px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Analizador de PDF - FORMATO.pdf</h1>
        <p>Esta herramienta analizará el archivo FORMATO.pdf para entender su estructura y ajustar los patrones de extracción.</p>
        
        <button onclick="loadAndAnalyzePDF()">📄 Cargar y Analizar FORMATO.pdf</button>
        <button onclick="testCurrentPatterns()">🧪 Probar Patrones Actuales</button>
        <button onclick="generateNewPatterns()">⚙️ Generar Nuevos Patrones</button>
    </div>

    <div class="container">
        <h2>📋 Texto Extraído del PDF</h2>
        <div id="extractedText" class="text-output">
            Haz clic en "Cargar y Analizar FORMATO.pdf" para ver el contenido...
        </div>
    </div>

    <div class="container">
        <h2>🔍 Análisis de Patrones</h2>
        <div id="patternAnalysis" class="analysis">
            El análisis aparecerá aquí después de cargar el PDF...
        </div>
    </div>

    <div class="container">
        <h2>⚙️ Patrones Recomendados</h2>
        <div id="recommendedPatterns" class="text-output">
            Los patrones recomendados aparecerán aquí...
        </div>
    </div>

    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    
    <script>
        // Configurar PDF.js
        const pdfjsLib = window['pdfjs-dist/build/pdf'];
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        
        let extractedText = '';
        
        async function loadAndAnalyzePDF() {
            try {
                document.getElementById('extractedText').textContent = 'Cargando PDF...';
                
                // Cargar el PDF
                const response = await fetch('FORMATO.pdf');
                const arrayBuffer = await response.arrayBuffer();
                const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                
                // Extraer texto de todas las páginas
                let fullText = '';
                for (let i = 1; i <= pdf.numPages; i++) {
                    const page = await pdf.getPage(i);
                    const textContent = await page.getTextContent();
                    const pageText = textContent.items.map(item => item.str).join(' ');
                    fullText += `=== PÁGINA ${i} ===\n${pageText}\n\n`;
                }
                
                extractedText = fullText;
                document.getElementById('extractedText').textContent = fullText;
                
                // Mostrar información básica
                document.getElementById('patternAnalysis').innerHTML = `
                    <div class="success">✅ PDF cargado exitosamente</div>
                    <p><strong>Páginas:</strong> ${pdf.numPages}</p>
                    <p><strong>Caracteres totales:</strong> ${fullText.length}</p>
                    <p><strong>Líneas:</strong> ${fullText.split('\n').length}</p>
                `;
                
                // Analizar automáticamente
                analyzeText(fullText);
                
            } catch (error) {
                console.error('Error cargando PDF:', error);
                document.getElementById('extractedText').innerHTML = `
                    <div class="error">❌ Error cargando PDF: ${error.message}</div>
                `;
            }
        }
        
        function analyzeText(text) {
            const analysis = document.getElementById('patternAnalysis');
            let analysisHTML = '<h3>📊 Análisis del Contenido</h3>';
            
            // Buscar posibles clientes
            const clientePatterns = [
                /(?:cliente|client|empresa|company|para|to|señor|sr|sra)[\s:]+([^\n\r]+)/gi,
                /(?:razón\s+social|business\s+name|facturar\s+a)[\s:]+([^\n\r]+)/gi
            ];
            
            analysisHTML += '<h4>🏢 Posibles Clientes Detectados:</h4>';
            clientePatterns.forEach((pattern, index) => {
                const matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    analysisHTML += `<div class="pattern-match">Patrón ${index + 1}: ${matches.length} coincidencias</div>`;
                    matches.forEach(match => {
                        analysisHTML += `<div style="margin-left: 20px;">• "${match[1]?.trim()}"</div>`;
                    });
                }
            });
            
            // Buscar fechas
            const fechaPatterns = [
                /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/g,
                /(?:fecha|date)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/gi
            ];
            
            analysisHTML += '<h4>📅 Fechas Detectadas:</h4>';
            fechaPatterns.forEach((pattern, index) => {
                const matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    analysisHTML += `<div class="pattern-match">Patrón ${index + 1}: ${matches.length} coincidencias</div>`;
                    matches.forEach(match => {
                        analysisHTML += `<div style="margin-left: 20px;">• "${match[0]}"</div>`;
                    });
                }
            });
            
            // Buscar números de cotización
            const cotizacionPatterns = [
                /(?:cotización|quote|cot|no|número|#|folio)[\s\.:]*([A-Z0-9\-]+)/gi,
                /(?:ref|referencia)[\s\.:]*([A-Z0-9\-]+)/gi
            ];
            
            analysisHTML += '<h4>📋 Números de Cotización:</h4>';
            cotizacionPatterns.forEach((pattern, index) => {
                const matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    analysisHTML += `<div class="pattern-match">Patrón ${index + 1}: ${matches.length} coincidencias</div>`;
                    matches.forEach(match => {
                        analysisHTML += `<div style="margin-left: 20px;">• "${match[1]?.trim()}"</div>`;
                    });
                }
            });
            
            // Buscar precios
            const precioPatterns = [
                /\$[\d,]+(?:\.\d{2})?/g,
                /[\d,]+\.\d{2}/g
            ];
            
            analysisHTML += '<h4>💰 Precios Detectados:</h4>';
            precioPatterns.forEach((pattern, index) => {
                const matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    analysisHTML += `<div class="pattern-match">Patrón ${index + 1}: ${matches.length} coincidencias</div>`;
                    const uniquePrices = [...new Set(matches.map(m => m[0]))].slice(0, 10);
                    uniquePrices.forEach(price => {
                        analysisHTML += `<div style="margin-left: 20px;">• "${price}"</div>`;
                    });
                    if (matches.length > 10) {
                        analysisHTML += `<div style="margin-left: 20px;"><em>... y ${matches.length - 10} más</em></div>`;
                    }
                }
            });
            
            // Buscar líneas que parecen artículos
            const lines = text.split('\n').filter(line => line.trim().length > 10);
            const possibleItems = lines.filter(line => {
                // Líneas que contienen números y texto
                return /\d/.test(line) && line.length > 20 && line.length < 200;
            });
            
            analysisHTML += '<h4>🛍️ Posibles Líneas de Artículos:</h4>';
            analysisHTML += `<div class="pattern-match">Encontradas ${possibleItems.length} líneas candidatas</div>`;
            possibleItems.slice(0, 10).forEach(item => {
                analysisHTML += `<div style="margin-left: 20px;">• "${item.trim()}"</div>`;
            });
            if (possibleItems.length > 10) {
                analysisHTML += `<div style="margin-left: 20px;"><em>... y ${possibleItems.length - 10} más</em></div>`;
            }
            
            analysis.innerHTML = analysisHTML;
        }
        
        function testCurrentPatterns() {
            if (!extractedText) {
                alert('Primero carga el PDF');
                return;
            }
            
            // Simular el procesamiento actual
            const analysis = document.getElementById('patternAnalysis');
            analysis.innerHTML += `
                <h3>🧪 Prueba de Patrones Actuales</h3>
                <div class="error">Probando con los patrones implementados actualmente...</div>
            `;
            
            // Aquí simularíamos el procesamiento actual
            setTimeout(() => {
                analysis.innerHTML += `
                    <div class="pattern-match">Los patrones actuales pueden no ser óptimos para este formato específico.</div>
                `;
            }, 1000);
        }
        
        function generateNewPatterns() {
            if (!extractedText) {
                alert('Primero carga el PDF');
                return;
            }
            
            const patterns = document.getElementById('recommendedPatterns');
            
            // Analizar el texto y generar patrones específicos
            const lines = extractedText.split('\n').filter(line => line.trim().length > 5);
            
            let recommendations = `// PATRONES RECOMENDADOS BASADOS EN FORMATO.pdf\n\n`;
            
            // Generar patrones de cliente basados en el contenido real
            recommendations += `// Patrones de Cliente\ncliente: [\n`;
            
            // Buscar líneas que podrían contener información de cliente
            const clientLines = lines.filter(line => 
                /(?:cliente|empresa|para|señor|sr|sra)/i.test(line) && line.length < 100
            );
            
            if (clientLines.length > 0) {
                recommendations += `    // Basado en: "${clientLines[0].trim()}"\n`;
                recommendations += `    /pattern_here/i,\n`;
            }
            
            recommendations += `],\n\n`;
            
            // Patrones de fecha
            recommendations += `// Patrones de Fecha\nfecha: [\n`;
            const dateMatches = [...extractedText.matchAll(/\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/g)];
            if (dateMatches.length > 0) {
                recommendations += `    // Encontradas fechas como: ${dateMatches.slice(0, 3).map(m => m[0]).join(', ')}\n`;
                recommendations += `    /\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}/g,\n`;
            }
            recommendations += `],\n\n`;
            
            // Mostrar estructura de líneas para artículos
            recommendations += `// Estructura de Líneas (para artículos)\n`;
            recommendations += `// Líneas típicas encontradas:\n`;
            
            const sampleLines = lines.filter(line => 
                line.length > 20 && line.length < 150 && /\d/.test(line)
            ).slice(0, 5);
            
            sampleLines.forEach((line, index) => {
                recommendations += `// ${index + 1}. "${line.trim()}"\n`;
            });
            
            patterns.textContent = recommendations;
        }
        
        // Cargar automáticamente al abrir la página
        window.addEventListener('load', () => {
            setTimeout(loadAndAnalyzePDF, 1000);
        });
    </script>
</body>
</html>

/**
 * Improved PDF Processor Module
 * Enhanced version with better error handling and timeout management
 */

class ImprovedPDFProcessor {
    constructor() {
        this.pdfjsLib = window['pdfjs-dist/build/pdf'];
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        
        // Timeout configurations
        this.timeouts = {
            fileRead: 10000,      // 10 seconds for file reading
            pdfLoad: 15000,       // 15 seconds for PDF loading
            pageProcess: 5000,    // 5 seconds per page
            totalProcess: 60000   // 60 seconds total per file
        };
        
        // Improved patterns based on common Spanish invoice formats
        this.patterns = {
            // Cliente patterns - more comprehensive
            cliente: [
                /(?:cliente|client)[\s:]+([^\n\r]+)/i,
                /(?:empresa|company)[\s:]+([^\n\r]+)/i,
                /(?:para|to|dirigido\s+a)[\s:]+([^\n\r]+)/i,
                /(?:señor|sr|sra|señora|mr|mrs)[\s\.]+([^\n\r]+)/i,
                /(?:razón\s+social|business\s+name)[\s:]+([^\n\r]+)/i,
                /(?:facturar\s+a|bill\s+to)[\s:]+([^\n\r]+)/i,
                // Pattern for lines that start with a name (common in headers)
                /^([A-ZÁÉÍÓÚÑ][a-záéíóúñ]+(?:\s+[A-ZÁÉÍÓÚÑ][a-záéíóúñ]+)*(?:\s+S\.?A\.?(?:\s+de\s+C\.?V\.?)?|\s+LTDA\.?|\s+S\.?L\.?)?)$/m
            ],
            
            // Fecha patterns - more flexible
            fecha: [
                /(?:fecha|date)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
                /(?:cotización|quote|presupuesto)[\s\w]*?(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
                /(?:válida?\s+hasta|valid\s+until)[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
                // Standalone date patterns
                /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4})/g,
                /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2})/g
            ],
            
            // Número de cotización patterns
            numeroCotizacion: [
                /(?:cotización|quote|cot)[\s\.:]*(?:no\.?|número|#)?[\s]*([A-Z0-9\-]+)/i,
                /(?:folio|ref|referencia)[\s\.:]*([A-Z0-9\-]+)/i,
                /(?:presupuesto|budget)[\s\.:]*(?:no\.?|número|#)?[\s]*([A-Z0-9\-]+)/i,
                /#[\s]*([A-Z0-9\-]+)/i
            ],
            
            // Totales patterns - more comprehensive
            totales: {
                total: [
                    /(?:total|grand\s+total|importe\s+total)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i,
                    /(?:total\s+general|total\s+final)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i
                ],
                subtotal: [
                    /(?:subtotal|sub\s+total|importe\s+neto)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i,
                    /(?:suma|subtotal\s+antes)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i
                ],
                iva: [
                    /(?:iva|i\.v\.a\.?|tax|impuesto)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i,
                    /(?:16%|15%|21%)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i
                ],
                descuento: [
                    /(?:descuento|discount|rebaja)[\s:]*\$?\s*([\d,]+(?:\.\d{2})?)/i
                ]
            }
        };
    }

    /**
     * Process a single PDF file with timeout protection
     * @param {File} file - PDF file to process
     * @returns {Promise<Object>} Extracted data
     */
    async processPDF(file) {
        const startTime = Date.now();
        
        return new Promise(async (resolve, reject) => {
            // Set overall timeout
            const timeoutId = setTimeout(() => {
                reject(new Error(`Timeout: El procesamiento de ${file.name} excedió ${this.timeouts.totalProcess/1000} segundos`));
            }, this.timeouts.totalProcess);
            
            try {
                console.log(`Iniciando procesamiento de: ${file.name}`);
                
                // Step 1: Convert file to ArrayBuffer with timeout
                const arrayBuffer = await this.fileToArrayBufferWithTimeout(file);
                console.log(`ArrayBuffer creado para: ${file.name}`);
                
                // Step 2: Load PDF with timeout
                const pdf = await this.loadPDFWithTimeout(arrayBuffer);
                console.log(`PDF cargado: ${file.name}, páginas: ${pdf.numPages}`);
                
                // Step 3: Extract text from all pages
                const { fullText, pageTexts } = await this.extractTextFromPages(pdf);
                console.log(`Texto extraído de: ${file.name}, caracteres: ${fullText.length}`);
                
                // Step 4: Parse extracted data
                const extractedData = this.parseExtractedText(fullText, file.name);
                console.log(`Datos parseados para: ${file.name}`);
                
                const result = {
                    fileName: file.name,
                    fileSize: file.size,
                    pageCount: pdf.numPages,
                    rawText: fullText,
                    pageTexts: pageTexts,
                    extractedData: extractedData,
                    processedAt: new Date().toISOString(),
                    processingTime: Date.now() - startTime
                };
                
                clearTimeout(timeoutId);
                resolve(result);
                
            } catch (error) {
                clearTimeout(timeoutId);
                console.error(`Error processing ${file.name}:`, error);
                reject(new Error(`Error procesando ${file.name}: ${error.message}`));
            }
        });
    }

    /**
     * Convert File to ArrayBuffer with timeout
     * @param {File} file 
     * @returns {Promise<ArrayBuffer>}
     */
    fileToArrayBufferWithTimeout(file) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Timeout leyendo archivo'));
            }, this.timeouts.fileRead);
            
            const reader = new FileReader();
            reader.onload = () => {
                clearTimeout(timeoutId);
                resolve(reader.result);
            };
            reader.onerror = () => {
                clearTimeout(timeoutId);
                reject(reader.error);
            };
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Load PDF with timeout
     * @param {ArrayBuffer} arrayBuffer 
     * @returns {Promise<Object>}
     */
    loadPDFWithTimeout(arrayBuffer) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Timeout cargando PDF'));
            }, this.timeouts.pdfLoad);
            
            this.pdfjsLib.getDocument(arrayBuffer).promise
                .then(pdf => {
                    clearTimeout(timeoutId);
                    resolve(pdf);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    }

    /**
     * Extract text from all pages with individual timeouts
     * @param {Object} pdf 
     * @returns {Promise<Object>}
     */
    async extractTextFromPages(pdf) {
        let fullText = '';
        const pageTexts = [];
        
        for (let i = 1; i <= pdf.numPages; i++) {
            try {
                const pageText = await this.extractPageTextWithTimeout(pdf, i);
                pageTexts.push(pageText);
                fullText += pageText + '\n';
            } catch (error) {
                console.warn(`Error extrayendo texto de página ${i}:`, error);
                pageTexts.push('');
                fullText += `[Error en página ${i}]\n`;
            }
        }
        
        return { fullText, pageTexts };
    }

    /**
     * Extract text from a single page with timeout
     * @param {Object} pdf 
     * @param {number} pageNum 
     * @returns {Promise<string>}
     */
    extractPageTextWithTimeout(pdf, pageNum) {
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Timeout procesando página ${pageNum}`));
            }, this.timeouts.pageProcess);
            
            try {
                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();
                
                // Join text items with proper spacing
                const pageText = textContent.items.map(item => {
                    // Add space if the item has a significant gap from the previous one
                    return item.str;
                }).join(' ');
                
                clearTimeout(timeoutId);
                resolve(pageText);
                
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    /**
     * Parse extracted text with improved logic
     * @param {string} text - Raw text from PDF
     * @param {string} fileName - Original file name
     * @returns {Object} Parsed data
     */
    parseExtractedText(text, fileName) {
        console.log('Parseando texto extraído...');
        
        const data = {
            cliente: this.extractCliente(text),
            fecha: this.extractFecha(text),
            numeroCotizacion: this.extractNumeroCotizacion(text),
            articulos: this.extractArticulos(text),
            totales: this.extractTotales(text),
            fileName: fileName,
            rawTextLength: text.length
        };

        console.log('Datos extraídos:', {
            cliente: data.cliente,
            fecha: data.fecha,
            numeroCotizacion: data.numeroCotizacion,
            articulosCount: data.articulos.length,
            totales: data.totales
        });

        return this.cleanExtractedData(data);
    }

    /**
     * Extract client information with multiple strategies
     * @param {string} text 
     * @returns {string}
     */
    extractCliente(text) {
        // Try each pattern
        for (const pattern of this.patterns.cliente) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const cliente = match[1].trim().replace(/[^\w\s\.\-]/g, '');
                if (cliente.length > 3 && cliente.length < 100) {
                    return cliente;
                }
            }
        }
        
        // Fallback: look for company-like words in the first part of the document
        const firstPart = text.substring(0, 1000);
        const lines = firstPart.split('\n');
        
        for (const line of lines) {
            const cleanLine = line.trim();
            if (cleanLine.length > 10 && cleanLine.length < 80) {
                // Look for lines that might be company names
                if (/(?:S\.?A\.?|LTDA\.?|S\.?L\.?|CORP|INC|LLC)/i.test(cleanLine)) {
                    return cleanLine.replace(/[^\w\s\.\-]/g, '').trim();
                }
            }
        }
        
        return 'Cliente no identificado';
    }

    /**
     * Extract date with improved logic
     * @param {string} text 
     * @returns {string}
     */
    extractFecha(text) {
        for (const pattern of this.patterns.fecha) {
            const matches = text.match(pattern);
            if (matches) {
                const dateStr = matches[1] || matches[0];
                const normalized = this.normalizeDate(dateStr);
                if (normalized !== new Date().toISOString().split('T')[0]) {
                    return normalized;
                }
            }
        }
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Extract quote number
     * @param {string} text 
     * @returns {string}
     */
    extractNumeroCotizacion(text) {
        for (const pattern of this.patterns.numeroCotizacion) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const numero = match[1].trim();
                if (numero.length > 1 && numero.length < 50) {
                    return numero;
                }
            }
        }
        return 'N/A';
    }

    /**
     * Extract articles with improved detection
     * @param {string} text 
     * @returns {Array}
     */
    extractArticulos(text) {
        const articulos = [];
        const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 5);
        
        console.log(`Analizando ${lines.length} líneas para artículos...`);
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // Skip header lines
            if (this.isHeaderLine(line)) continue;
            
            // Strategy 1: Detect table-like structures
            const tableMatch = this.extractFromTableLine(line);
            if (tableMatch) {
                articulos.push(tableMatch);
                continue;
            }
            
            // Strategy 2: Look for price at end of line
            const priceMatch = this.extractFromPriceLine(line);
            if (priceMatch) {
                articulos.push(priceMatch);
            }
        }
        
        console.log(`Encontrados ${articulos.length} artículos`);
        return articulos;
    }

    /**
     * Check if line is a header
     * @param {string} line 
     * @returns {boolean}
     */
    isHeaderLine(line) {
        const headerKeywords = [
            'cantidad', 'descripción', 'precio', 'total', 'importe',
            'qty', 'description', 'price', 'amount',
            'cant', 'desc', 'unit'
        ];
        
        const lowerLine = line.toLowerCase();
        return headerKeywords.some(keyword => lowerLine.includes(keyword)) && 
               !(/\d/.test(line) && /\$/.test(line));
    }

    /**
     * Extract from table-like line
     * @param {string} line 
     * @returns {Object|null}
     */
    extractFromTableLine(line) {
        // Pattern: quantity description price
        const patterns = [
            /^(\d+(?:\.\d+)?)\s+(.+?)\s+\$?\s*([\d,]+(?:\.\d{2})?)$/,
            /^(\d+)\s+(.+?)\s+(\d+(?:,\d{3})*(?:\.\d{2})?)$/,
            /(\d+(?:\.\d+)?)\s+(.{10,}?)\s+\$?([\d,]+(?:\.\d{2})?)$/
        ];
        
        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                const cantidad = parseFloat(match[1]);
                const descripcion = match[2].trim();
                const precio = this.parsePrice(match[3]);
                
                if (descripcion.length > 3 && precio > 0) {
                    return {
                        cantidad: cantidad,
                        descripcion: descripcion,
                        precio: precio,
                        total: cantidad * precio
                    };
                }
            }
        }
        
        return null;
    }

    /**
     * Extract from line ending with price
     * @param {string} line 
     * @returns {Object|null}
     */
    extractFromPriceLine(line) {
        const pricePattern = /(.+?)\s+\$?\s*([\d,]+(?:\.\d{2})?)$/;
        const match = line.match(pricePattern);
        
        if (match && line.length > 15) {
            const descripcion = match[1].trim();
            const precio = this.parsePrice(match[2]);
            
            if (descripcion.length > 5 && precio > 0 && 
                !this.isHeaderLine(descripcion) && 
                !/(total|subtotal|iva|descuento)/i.test(descripcion)) {
                
                return {
                    cantidad: 1,
                    descripcion: descripcion,
                    precio: precio,
                    total: precio
                };
            }
        }
        
        return null;
    }

    /**
     * Extract totals with improved patterns
     * @param {string} text 
     * @returns {Object}
     */
    extractTotales(text) {
        const totales = {};
        
        // Extract each type of total
        for (const [key, patterns] of Object.entries(this.patterns.totales)) {
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match && match[1]) {
                    totales[key] = this.parsePrice(match[1]);
                    break; // Use first match found
                }
            }
        }
        
        return totales;
    }

    /**
     * Parse price string to number
     * @param {string} priceStr 
     * @returns {number}
     */
    parsePrice(priceStr) {
        if (!priceStr) return 0;
        // Remove currency symbols and parse
        const cleaned = priceStr.toString().replace(/[$,\s]/g, '');
        return parseFloat(cleaned) || 0;
    }

    /**
     * Normalize date format
     * @param {string} dateStr 
     * @returns {string}
     */
    normalizeDate(dateStr) {
        try {
            // Handle different date formats
            let normalized = dateStr.replace(/[\/\-\.]/g, '/');
            
            // Try to parse the date
            const date = new Date(normalized);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
            
            // If that fails, try different format
            const parts = normalized.split('/');
            if (parts.length === 3) {
                // Assume DD/MM/YYYY format
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // Month is 0-indexed
                const year = parseInt(parts[2]);
                
                if (year < 100) year += 2000; // Handle 2-digit years
                
                const date2 = new Date(year, month, day);
                if (!isNaN(date2.getTime())) {
                    return date2.toISOString().split('T')[0];
                }
            }
        } catch (error) {
            console.warn('Error parsing date:', dateStr, error);
        }
        
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Clean and validate extracted data
     * @param {Object} data 
     * @returns {Object}
     */
    cleanExtractedData(data) {
        // Clean cliente name
        if (data.cliente && data.cliente !== 'Cliente no identificado') {
            data.cliente = data.cliente.replace(/[^\w\s\.\-]/g, '').trim();
            if (data.cliente.length > 100) {
                data.cliente = data.cliente.substring(0, 100) + '...';
            }
        }
        
        // Validate and clean articles
        data.articulos = data.articulos.filter(item => 
            item.descripcion && 
            item.descripcion.length > 2 && 
            item.precio > 0 &&
            item.cantidad > 0
        );
        
        // Remove duplicate articles (same description)
        const uniqueArticulos = [];
        const seen = new Set();
        
        for (const item of data.articulos) {
            const key = item.descripcion.toLowerCase().trim();
            if (!seen.has(key)) {
                seen.add(key);
                uniqueArticulos.push(item);
            }
        }
        
        data.articulos = uniqueArticulos;
        
        // Calculate totals if not found
        if (data.articulos.length > 0 && !data.totales.total) {
            data.totales.total = data.articulos.reduce((sum, item) => sum + (item.total || 0), 0);
        }
        
        return data;
    }

    /**
     * Process multiple PDF files with better error handling
     * @param {FileList} files 
     * @param {Function} progressCallback 
     * @returns {Promise<Array>}
     */
    async processMultiplePDFs(files, progressCallback) {
        const results = [];
        const total = files.length;
        
        console.log(`Iniciando procesamiento de ${total} archivos...`);
        
        for (let i = 0; i < total; i++) {
            const file = files[i];
            
            try {
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'processing'
                    });
                }
                
                const result = await this.processPDF(file);
                results.push(result);
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'completed'
                    });
                }
                
            } catch (error) {
                console.error(`Error processing ${file.name}:`, error);
                
                const errorResult = {
                    fileName: file.name,
                    error: error.message,
                    processedAt: new Date().toISOString()
                };
                
                results.push(errorResult);
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }
        
        console.log(`Procesamiento completado. ${results.filter(r => !r.error).length}/${total} archivos exitosos.`);
        return results;
    }
}

// Export for use in other modules
window.ImprovedPDFProcessor = ImprovedPDFProcessor;

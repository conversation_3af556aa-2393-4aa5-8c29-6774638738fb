/**
 * Improved PDF Processor Module
 * Enhanced version with better error handling and timeout management
 */

class ImprovedPDFProcessor {
    constructor() {
        this.pdfjsLib = window['pdfjs-dist/build/pdf'];
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        
        // Timeout configurations
        this.timeouts = {
            fileRead: 10000,      // 10 seconds for file reading
            pdfLoad: 15000,       // 15 seconds for PDF loading
            pageProcess: 5000,    // 5 seconds per page
            totalProcess: 60000   // 60 seconds total per file
        };
        
        // Patterns específicos para el formato AutoExcel S.A. de C.V
        this.patterns = {
            // Patrón específico para "Nombre:" (línea completa después de Nombre:)
            nombre: [
                /Nombre:\s*([^\n\r]+)/i,
                /NOMBRE:\s*([^\n\r]+)/i
            ],

            // Patrón específico para "Fecha:" (formato DD/MM/YYYY)
            fecha: [
                /Fecha:\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
                /FECHA:\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
                // Fallback patterns
                /(\d{1,2}\/\d{1,2}\/\d{4})/g
            ],

            // Patrón específico para "OrdenRepuestos:" - basado en texto real extraído
            ordenRepuestos: [
                // El número 250003367 aparece después de "OrdenRepuestos:" en el texto
                /OrdenRepuestos:[\s\S]*?(\d{9})/i,
                // Buscar directamente el patrón de 9 dígitos que vimos en el log
                /(250003367)/,
                // Patrón genérico para números de 8-10 dígitos
                /(\d{8,10})/g
            ],

            // Patrón específico para "Cotización:" - basado en texto real extraído
            cotizacion: [
                // El número 0000071551 aparece al inicio del texto
                /^(\d{10})/,
                // Buscar directamente el patrón que vimos en el log
                /(0000071551)/,
                // Patrón después de "Cotización:"
                /Cotización:[\s\S]*?(\d{8,})/i
            ],
            
            // Patrones específicos para totales del formato AutoExcel
            totales: {
                // Total: 2,939.17
                total: [
                    /Total:\s*([\d,]+\.?\d*)/i,
                    /TOTAL:\s*([\d,]+\.?\d*)/i
                ],
                // SubTotal: 3,932.00
                subtotal: [
                    /SubTotal:\s*([\d,]+\.?\d*)/i,
                    /SUBTOTAL:\s*([\d,]+\.?\d*)/i,
                    /Sub\s*Total:\s*([\d,]+\.?\d*)/i
                ],
                // IVA Total: 383.37
                iva: [
                    /IVA\s*Total:\s*([\d,]+\.?\d*)/i,
                    /IVA\s*TOTAL:\s*([\d,]+\.?\d*)/i,
                    /I\.V\.A\.?\s*Total:\s*([\d,]+\.?\d*)/i
                ],
                // Descuento: -1,376.20
                descuento: [
                    /Descuento:\s*(-?[\d,]+\.?\d*)/i,
                    /DESCUENTO:\s*(-?[\d,]+\.?\d*)/i
                ],
                // Venta Neta: 2,555.80
                ventaNeta: [
                    /Venta\s*Neta:\s*([\d,]+\.?\d*)/i,
                    /VENTA\s*NETA:\s*([\d,]+\.?\d*)/i
                ]
            }
        };
    }

    /**
     * Process a single PDF file with timeout protection
     * @param {File} file - PDF file to process
     * @returns {Promise<Object>} Extracted data
     */
    async processPDF(file) {
        const startTime = Date.now();
        
        return new Promise(async (resolve, reject) => {
            // Set overall timeout
            const timeoutId = setTimeout(() => {
                reject(new Error(`Timeout: El procesamiento de ${file.name} excedió ${this.timeouts.totalProcess/1000} segundos`));
            }, this.timeouts.totalProcess);
            
            try {
                console.log(`Iniciando procesamiento de: ${file.name}`);
                
                // Step 1: Convert file to ArrayBuffer with timeout
                const arrayBuffer = await this.fileToArrayBufferWithTimeout(file);
                console.log(`ArrayBuffer creado para: ${file.name}`);
                
                // Step 2: Load PDF with timeout
                const pdf = await this.loadPDFWithTimeout(arrayBuffer);
                console.log(`PDF cargado: ${file.name}, páginas: ${pdf.numPages}`);
                
                // Step 3: Extract text from all pages
                const { fullText, pageTexts } = await this.extractTextFromPages(pdf);
                console.log(`Texto extraído de: ${file.name}, caracteres: ${fullText.length}`);
                
                // Step 4: Parse extracted data
                const extractedData = this.parseExtractedText(fullText, file.name);
                console.log(`Datos parseados para: ${file.name}`);
                
                const result = {
                    fileName: file.name,
                    fileSize: file.size,
                    pageCount: pdf.numPages,
                    rawText: fullText,
                    pageTexts: pageTexts,
                    extractedData: extractedData,
                    processedAt: new Date().toISOString(),
                    processingTime: Date.now() - startTime
                };
                
                clearTimeout(timeoutId);
                resolve(result);
                
            } catch (error) {
                clearTimeout(timeoutId);
                console.error(`Error processing ${file.name}:`, error);
                reject(new Error(`Error procesando ${file.name}: ${error.message}`));
            }
        });
    }

    /**
     * Convert File to ArrayBuffer with timeout
     * @param {File} file 
     * @returns {Promise<ArrayBuffer>}
     */
    fileToArrayBufferWithTimeout(file) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Timeout leyendo archivo'));
            }, this.timeouts.fileRead);
            
            const reader = new FileReader();
            reader.onload = () => {
                clearTimeout(timeoutId);
                resolve(reader.result);
            };
            reader.onerror = () => {
                clearTimeout(timeoutId);
                reject(reader.error);
            };
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Load PDF with timeout
     * @param {ArrayBuffer} arrayBuffer 
     * @returns {Promise<Object>}
     */
    loadPDFWithTimeout(arrayBuffer) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('Timeout cargando PDF'));
            }, this.timeouts.pdfLoad);
            
            this.pdfjsLib.getDocument(arrayBuffer).promise
                .then(pdf => {
                    clearTimeout(timeoutId);
                    resolve(pdf);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    }

    /**
     * Extract text from all pages with individual timeouts
     * @param {Object} pdf 
     * @returns {Promise<Object>}
     */
    async extractTextFromPages(pdf) {
        let fullText = '';
        const pageTexts = [];
        
        for (let i = 1; i <= pdf.numPages; i++) {
            try {
                const pageText = await this.extractPageTextWithTimeout(pdf, i);
                pageTexts.push(pageText);
                fullText += pageText + '\n';
            } catch (error) {
                console.warn(`Error extrayendo texto de página ${i}:`, error);
                pageTexts.push('');
                fullText += `[Error en página ${i}]\n`;
            }
        }
        
        return { fullText, pageTexts };
    }

    /**
     * Extract text from a single page with timeout
     * @param {Object} pdf
     * @param {number} pageNum
     * @returns {Promise<string>}
     */
    extractPageTextWithTimeout(pdf, pageNum) {
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Timeout procesando página ${pageNum}`));
            }, this.timeouts.pageProcess);

            try {
                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();

                // Join text items with better spacing
                let pageText = '';
                let lastY = null;

                textContent.items.forEach((item, index) => {
                    // Add line break if Y position changed significantly (new line)
                    if (lastY !== null && Math.abs(item.transform[5] - lastY) > 5) {
                        pageText += '\n';
                    } else if (index > 0) {
                        pageText += ' ';
                    }

                    pageText += item.str;
                    lastY = item.transform[5];
                });

                console.log(`Página ${pageNum} extraída, caracteres: ${pageText.length}`);

                clearTimeout(timeoutId);
                resolve(pageText);

            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    /**
     * Parse extracted text with improved logic
     * @param {string} text - Raw text from PDF
     * @param {string} fileName - Original file name
     * @returns {Object} Parsed data
     */
    parseExtractedText(text, fileName) {
        console.log('Parseando texto extraído...');

        const data = {
            nombre: this.extractNombre(text),
            fecha: this.extractFecha(text),
            ordenRepuestos: this.extractOrdenRepuestos(text),
            cotizacion: this.extractCotizacion(text),
            articulos: this.extractArticulos(text),
            totales: this.extractTotales(text),
            fileName: fileName,
            rawTextLength: text.length
        };

        console.log('Datos extraídos:', {
            nombre: data.nombre,
            fecha: data.fecha,
            ordenRepuestos: data.ordenRepuestos,
            cotizacion: data.cotizacion,
            articulosCount: data.articulos.length,
            totales: data.totales
        });

        return this.cleanExtractedData(data);
    }

    /**
     * Extract nombre information
     * @param {string} text
     * @returns {string}
     */
    extractNombre(text) {
        console.log('=== EXTRAYENDO NOMBRE ===');
        console.log('Texto a analizar (primeros 500 caracteres):', text.substring(0, 500));

        for (let i = 0; i < this.patterns.nombre.length; i++) {
            const pattern = this.patterns.nombre[i];
            console.log(`Probando patrón ${i + 1}:`, pattern);
            const match = text.match(pattern);
            if (match) {
                console.log('¡Coincidencia encontrada!', match);
                if (match[1]) {
                    const nombre = match[1].trim();
                    console.log('Nombre extraído:', nombre);
                    if (nombre.length > 0) {
                        return nombre;
                    }
                }
            } else {
                console.log('No hay coincidencia con este patrón');
            }
        }

        console.log('❌ No se encontró nombre, buscando manualmente...');
        // Buscar manualmente líneas que contengan "Nombre"
        const lines = text.split('\n');
        for (const line of lines) {
            if (line.toLowerCase().includes('nombre')) {
                console.log('Línea con "nombre" encontrada:', line);
            }
        }

        return 'Nombre no identificado';
    }

    /**
     * Extract orden repuestos information
     * @param {string} text
     * @returns {string}
     */
    extractOrdenRepuestos(text) {
        for (const pattern of this.patterns.ordenRepuestos) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const orden = match[1].trim();
                if (orden.length > 0) {
                    return orden;
                }
            }
        }
        return 'Orden no identificada';
    }

    /**
     * Extract cotización information
     * @param {string} text
     * @returns {string}
     */
    extractCotizacion(text) {
        for (const pattern of this.patterns.cotizacion) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const cotizacion = match[1].trim();
                if (cotizacion.length > 0) {
                    return cotizacion;
                }
            }
        }
        return 'Cotización no identificada';
    }

    /**
     * Extract date with improved logic
     * @param {string} text 
     * @returns {string}
     */
    extractFecha(text) {
        for (const pattern of this.patterns.fecha) {
            const matches = text.match(pattern);
            if (matches) {
                const dateStr = matches[1] || matches[0];
                const normalized = this.normalizeDate(dateStr);
                if (normalized !== new Date().toISOString().split('T')[0]) {
                    return normalized;
                }
            }
        }
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Extract quote number
     * @param {string} text 
     * @returns {string}
     */
    extractNumeroCotizacion(text) {
        for (const pattern of this.patterns.numeroCotizacion) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const numero = match[1].trim();
                if (numero.length > 1 && numero.length < 50) {
                    return numero;
                }
            }
        }
        return 'N/A';
    }

    /**
     * Extract articles with specific table structure
     * Estructura: Cant. Articulo Descripcion T.Facturacion Procedencia PrecioUni DescUni Total
     * @param {string} text
     * @returns {Array}
     */
    extractArticulos(text) {
        const articulos = [];
        const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 5);

        console.log(`Analizando ${lines.length} líneas para artículos...`);

        // Buscar el inicio de la tabla
        let tableStartIndex = -1;
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].toLowerCase();
            if (line.includes('cant') && line.includes('articulo') && line.includes('descripcion')) {
                tableStartIndex = i + 1; // Empezar después del encabezado
                console.log(`Tabla encontrada en línea ${i}: ${lines[i]}`);
                break;
            }
        }

        if (tableStartIndex === -1) {
            console.log('No se encontró encabezado de tabla, usando detección genérica...');
            return this.extractArticulosGeneric(text);
        }

        // Procesar líneas de la tabla
        for (let i = tableStartIndex; i < lines.length; i++) {
            const line = lines[i];

            // Parar si encontramos líneas que indican fin de tabla
            if (this.isEndOfTable(line)) {
                console.log(`Fin de tabla detectado en línea ${i}: ${line}`);
                break;
            }

            // Intentar extraer artículo de la línea
            console.log(`Procesando línea ${i}: "${line}"`);
            const articulo = this.extractFromAutoExcelTableRow(line);
            if (articulo) {
                articulos.push(articulo);
                console.log(`✅ Artículo extraído: ${articulo.descripcion} - $${articulo.precioUni}`);
            } else {
                console.log(`❌ No se pudo extraer artículo de: "${line}"`);
            }
        }

        console.log(`Encontrados ${articulos.length} artículos en tabla`);
        return articulos;
    }

    /**
     * Extract from AutoExcel table row
     * Estructura REAL: Total | PrecioUni | T.Facturacion | Cantidad | Descripción | DescUni
     * Ejemplo: "2,555.80 1,966.00 Detalle 2.00   FILTRO DE AIRE (C)   688.10"
     * @param {string} line
     * @returns {Object|null}
     */
    extractFromAutoExcelTableRow(line) {
        console.log(`🔍 Analizando línea: "${line}"`);

        // Patrón basado en la estructura REAL observada en el log
        // Total PrecioUni T.Facturacion Cantidad Descripción DescUni
        const patterns = [
            // Patrón principal: Total PrecioUni Detalle Cantidad Descripción DescUni
            /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/,
            // Patrón alternativo sin DescUni al final
            /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+)$/,
            // Patrón simple: números + texto
            /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+.+?\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/
        ];

        for (let i = 0; i < patterns.length; i++) {
            const pattern = patterns[i];
            console.log(`  Probando patrón ${i + 1}:`, pattern);
            const match = line.match(pattern);

            if (match) {
                console.log(`  ✅ Coincidencia encontrada:`, match);

                // Estructura REAL: Total PrecioUni T.Facturacion Cantidad Descripción DescUni
                let total, precioUni, tFacturacion, cantidad, descripcion, descUni;

                if (match.length >= 6) {
                    // Patrón principal: Total PrecioUni Detalle Cantidad Descripción DescUni
                    total = this.parsePrice(match[1]);
                    precioUni = this.parsePrice(match[2]);
                    tFacturacion = match[3];
                    cantidad = parseFloat(match[4]);
                    descripcion = match[5].trim();
                    descUni = match[6] ? this.parsePrice(match[6]) : 0;
                } else if (match.length === 6) {
                    // Patrón alternativo sin DescUni
                    total = this.parsePrice(match[1]);
                    precioUni = this.parsePrice(match[2]);
                    tFacturacion = match[3];
                    cantidad = parseFloat(match[4]);
                    descripcion = match[5].trim();
                    descUni = 0;
                }

                // Limpiar descripción
                descripcion = descripcion.replace(/\s+$/, '').trim();

                if (descripcion.length > 3 && cantidad > 0 && total > 0) {
                    const result = {
                        cantidad: cantidad,
                        articulo: descripcion.split(' ')[0] || '', // Primera palabra como artículo
                        descripcion: descripcion,
                        tFacturacion: tFacturacion || 'Detalle',
                        procedencia: '', // No especificado en este formato
                        precioUni: precioUni,
                        descUni: descUni,
                        total: total
                    };

                    console.log(`  ✅ Artículo extraído exitosamente:`, result);
                    return result;
                }
            } else {
                console.log(`  ❌ No coincide con patrón ${i + 1}`);
            }
        }

        return null;
    }

    /**
     * Check if line indicates end of table
     * @param {string} line
     * @returns {boolean}
     */
    isEndOfTable(line) {
        const endKeywords = [
            'subtotal:', 'total:', 'iva total:', 'descuento:', 'venta neta:',
            'suma:', 'importe:', 'gran total:'
        ];

        const lowerLine = line.toLowerCase();
        // Solo considerar fin de tabla si tiene ":" después de la palabra clave
        const hasEndKeyword = endKeywords.some(keyword => lowerLine.includes(keyword));

        console.log(`Verificando fin de tabla para: "${line}" -> ${hasEndKeyword}`);

        return hasEndKeyword;
    }

    /**
     * Alternative article extraction for fallback
     * @param {string} text
     * @returns {Array}
     */
    extractArticulosGeneric(text) {
        const articulos = [];
        const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 10);

        for (const line of lines) {
            // Skip obvious header or total lines
            if (this.isHeaderLine(line) || this.isEndOfTable(line)) continue;

            // Try to extract using generic patterns
            const articulo = this.extractFromTableLine(line) || this.extractFromPriceLine(line);
            if (articulo) {
                articulos.push(articulo);
            }
        }

        return articulos;
    }

    /**
     * Check if line is a header
     * @param {string} line
     * @returns {boolean}
     */
    isHeaderLine(line) {
        const headerKeywords = [
            'cantidad', 'descripción', 'precio', 'total', 'importe',
            'qty', 'description', 'price', 'amount',
            'cant', 'desc', 'unit', 'articulo', 'facturacion', 'procedencia'
        ];

        const lowerLine = line.toLowerCase();
        return headerKeywords.some(keyword => lowerLine.includes(keyword)) &&
               !(/\d/.test(line) && /[\d,]+\.?\d*/.test(line));
    }

    /**
     * Extract from table-like line
     * @param {string} line 
     * @returns {Object|null}
     */
    extractFromTableLine(line) {
        // Pattern: quantity description price
        const patterns = [
            /^(\d+(?:\.\d+)?)\s+(.+?)\s+\$?\s*([\d,]+(?:\.\d{2})?)$/,
            /^(\d+)\s+(.+?)\s+(\d+(?:,\d{3})*(?:\.\d{2})?)$/,
            /(\d+(?:\.\d+)?)\s+(.{10,}?)\s+\$?([\d,]+(?:\.\d{2})?)$/
        ];
        
        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                const cantidad = parseFloat(match[1]);
                const descripcion = match[2].trim();
                const precio = this.parsePrice(match[3]);
                
                if (descripcion.length > 3 && precio > 0) {
                    return {
                        cantidad: cantidad,
                        descripcion: descripcion,
                        precio: precio,
                        total: cantidad * precio
                    };
                }
            }
        }
        
        return null;
    }

    /**
     * Extract from line ending with price
     * @param {string} line 
     * @returns {Object|null}
     */
    extractFromPriceLine(line) {
        const pricePattern = /(.+?)\s+\$?\s*([\d,]+(?:\.\d{2})?)$/;
        const match = line.match(pricePattern);
        
        if (match && line.length > 15) {
            const descripcion = match[1].trim();
            const precio = this.parsePrice(match[2]);
            
            if (descripcion.length > 5 && precio > 0 && 
                !this.isHeaderLine(descripcion) && 
                !/(total|subtotal|iva|descuento)/i.test(descripcion)) {
                
                return {
                    cantidad: 1,
                    descripcion: descripcion,
                    precio: precio,
                    total: precio
                };
            }
        }
        
        return null;
    }

    /**
     * Extract totals with improved patterns
     * @param {string} text 
     * @returns {Object}
     */
    extractTotales(text) {
        const totales = {};
        
        // Extract each type of total
        for (const [key, patterns] of Object.entries(this.patterns.totales)) {
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match && match[1]) {
                    totales[key] = this.parsePrice(match[1]);
                    break; // Use first match found
                }
            }
        }
        
        return totales;
    }

    /**
     * Parse price string to number
     * @param {string} priceStr 
     * @returns {number}
     */
    parsePrice(priceStr) {
        if (!priceStr) return 0;
        // Remove currency symbols and parse
        const cleaned = priceStr.toString().replace(/[$,\s]/g, '');
        return parseFloat(cleaned) || 0;
    }

    /**
     * Normalize date format
     * @param {string} dateStr
     * @returns {string}
     */
    normalizeDate(dateStr) {
        try {
            console.log('Normalizando fecha:', dateStr);

            // Handle different date formats
            let normalized = dateStr.replace(/[\/\-\.]/g, '/');

            // Parse DD/MM/YYYY format specifically
            const parts = normalized.split('/');
            if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // Month is 0-indexed
                const year = parseInt(parts[2]);

                console.log('Partes de fecha:', { day, month: month + 1, year });

                const date = new Date(year, month, day);
                if (!isNaN(date.getTime())) {
                    const result = date.toISOString().split('T')[0];
                    console.log('Fecha normalizada:', result);
                    return result;
                }
            }

            // Fallback: try direct parsing
            const date = new Date(normalized);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }

        } catch (error) {
            console.warn('Error parsing date:', dateStr, error);
        }

        return new Date().toISOString().split('T')[0];
    }

    /**
     * Clean and validate extracted data
     * @param {Object} data
     * @returns {Object}
     */
    cleanExtractedData(data) {
        // Clean nombre
        if (data.nombre && data.nombre !== 'Nombre no identificado') {
            data.nombre = data.nombre.trim();
            if (data.nombre.length > 100) {
                data.nombre = data.nombre.substring(0, 100) + '...';
            }
        }

        // Clean ordenRepuestos and cotizacion
        if (data.ordenRepuestos) {
            data.ordenRepuestos = data.ordenRepuestos.trim();
        }
        if (data.cotizacion) {
            data.cotizacion = data.cotizacion.trim();
        }

        // Validate and clean articles
        data.articulos = data.articulos.filter(item =>
            item.descripcion &&
            item.descripcion.length > 2 &&
            (item.precioUni > 0 || item.total > 0) &&
            item.cantidad > 0
        );

        // Remove duplicate articles (same description)
        const uniqueArticulos = [];
        const seen = new Set();

        for (const item of data.articulos) {
            const key = item.descripcion.toLowerCase().trim();
            if (!seen.has(key)) {
                seen.add(key);
                uniqueArticulos.push(item);
            }
        }

        data.articulos = uniqueArticulos;

        // Calculate totals if not found
        if (data.articulos.length > 0 && !data.totales.total) {
            data.totales.total = data.articulos.reduce((sum, item) => sum + (item.total || 0), 0);
        }

        return data;
    }

    /**
     * Process multiple PDF files with better error handling
     * @param {FileList} files 
     * @param {Function} progressCallback 
     * @returns {Promise<Array>}
     */
    async processMultiplePDFs(files, progressCallback) {
        const results = [];
        const total = files.length;
        
        console.log(`Iniciando procesamiento de ${total} archivos...`);
        
        for (let i = 0; i < total; i++) {
            const file = files[i];
            
            try {
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'processing'
                    });
                }
                
                const result = await this.processPDF(file);
                results.push(result);
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'completed'
                    });
                }
                
            } catch (error) {
                console.error(`Error processing ${file.name}:`, error);
                
                const errorResult = {
                    fileName: file.name,
                    error: error.message,
                    processedAt: new Date().toISOString()
                };
                
                results.push(errorResult);
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: file.name,
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }
        
        console.log(`Procesamiento completado. ${results.filter(r => !r.error).length}/${total} archivos exitosos.`);
        return results;
    }
}

// Export for use in other modules
window.ImprovedPDFProcessor = ImprovedPDFProcessor;

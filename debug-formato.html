<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug FORMATO.pdf - Diagnóstico Completo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .text-output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .highlight { background: yellow; font-weight: bold; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug FORMATO.pdf - Diagnóstico Completo</h1>
        <p>Esta herramienta mostrará exactamente qué está extrayendo del PDF para identificar el problema.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="debugPDF()">🚀 Analizar FORMATO.pdf</button>
            <button onclick="showRawText()">📄 Mostrar Texto Completo</button>
            <button onclick="searchPatterns()">🔍 Buscar Patrones</button>
        </div>
    </div>

    <div id="statusContainer" class="container">
        <h2>📊 Estado del Procesamiento</h2>
        <div id="status">Haz clic en "Analizar FORMATO.pdf" para comenzar...</div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>📄 Texto Extraído (Primeros 2000 caracteres)</h2>
            <div id="extractedText" class="text-output">Esperando análisis...</div>
        </div>
        
        <div class="container">
            <h2>🔍 Búsqueda de Patrones</h2>
            <div id="patternResults" class="text-output">Esperando análisis...</div>
        </div>
    </div>

    <div class="container">
        <h2>📋 Líneas del PDF (Primeras 50)</h2>
        <div id="pdfLines" class="text-output">Esperando análisis...</div>
    </div>

    <div class="container">
        <h2>🎯 Resultados de Extracción</h2>
        <div id="extractionResults" class="text-output">Esperando análisis...</div>
    </div>

    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        let fullTextData = '';
        let extractedData = null;
        
        async function debugPDF() {
            try {
                updateStatus('🔄 Cargando FORMATO.pdf...', 'info');
                
                const response = await fetch('FORMATO.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'FORMATO.pdf', { type: 'application/pdf' });
                
                updateStatus('✅ PDF cargado, iniciando procesamiento...', 'success');
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                fullTextData = result.rawText;
                extractedData = result.extractedData;
                
                updateStatus('✅ Procesamiento completado', 'success');
                
                // Mostrar texto extraído
                document.getElementById('extractedText').textContent = fullTextData.substring(0, 2000) + 
                    (fullTextData.length > 2000 ? '\n\n... (texto truncado, total: ' + fullTextData.length + ' caracteres)' : '');
                
                // Mostrar líneas
                showLines();
                
                // Mostrar resultados
                showExtractionResults();
                
                // Buscar patrones automáticamente
                searchPatterns();
                
            } catch (error) {
                updateStatus('❌ Error: ' + error.message, 'error');
                console.error('Error completo:', error);
            }
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function showRawText() {
            if (!fullTextData) {
                alert('Primero ejecuta el análisis');
                return;
            }
            
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html>
                <head><title>Texto Completo - FORMATO.pdf</title></head>
                <body style="font-family: monospace; white-space: pre-wrap; padding: 20px;">
                ${fullTextData.replace(/</g, '&lt;').replace(/>/g, '&gt;')}
                </body>
                </html>
            `);
        }
        
        function showLines() {
            if (!fullTextData) return;
            
            const lines = fullTextData.split('\n');
            let output = 'TOTAL DE LÍNEAS: ' + lines.length + '\n\n';
            
            lines.slice(0, 50).forEach((line, index) => {
                output += `${(index + 1).toString().padStart(3, '0')}: ${line}\n`;
            });
            
            if (lines.length > 50) {
                output += '\n... (mostrando solo las primeras 50 líneas)';
            }
            
            document.getElementById('pdfLines').textContent = output;
        }
        
        function searchPatterns() {
            if (!fullTextData) {
                alert('Primero ejecuta el análisis');
                return;
            }
            
            let results = '=== BÚSQUEDA DE PATRONES ===\n\n';
            
            // Buscar "Nombre"
            results += '🔍 BUSCANDO "Nombre":\n';
            const nombreMatches = fullTextData.match(/.*nombre.*/gi);
            if (nombreMatches) {
                nombreMatches.forEach(match => {
                    results += `  ✅ "${match}"\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            // Buscar "Fecha"
            results += '\n🔍 BUSCANDO "Fecha":\n';
            const fechaMatches = fullTextData.match(/.*fecha.*/gi);
            if (fechaMatches) {
                fechaMatches.forEach(match => {
                    results += `  ✅ "${match}"\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            // Buscar "OrdenRepuestos"
            results += '\n🔍 BUSCANDO "OrdenRepuestos":\n';
            const ordenMatches = fullTextData.match(/.*orden.*repuestos.*/gi);
            if (ordenMatches) {
                ordenMatches.forEach(match => {
                    results += `  ✅ "${match}"\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            // Buscar "Cotización"
            results += '\n🔍 BUSCANDO "Cotización":\n';
            const cotizacionMatches = fullTextData.match(/.*cotizaci[oó]n.*/gi);
            if (cotizacionMatches) {
                cotizacionMatches.forEach(match => {
                    results += `  ✅ "${match}"\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            // Buscar números
            results += '\n🔍 BUSCANDO NÚMEROS LARGOS (posibles IDs):\n';
            const numeroMatches = fullTextData.match(/\d{6,}/g);
            if (numeroMatches) {
                const uniqueNumbers = [...new Set(numeroMatches)];
                uniqueNumbers.slice(0, 10).forEach(num => {
                    results += `  📊 ${num}\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            // Buscar fechas
            results += '\n🔍 BUSCANDO FECHAS (DD/MM/YYYY):\n';
            const fechaFormatMatches = fullTextData.match(/\d{1,2}\/\d{1,2}\/\d{4}/g);
            if (fechaFormatMatches) {
                fechaFormatMatches.forEach(fecha => {
                    results += `  📅 ${fecha}\n`;
                });
            } else {
                results += '  ❌ No encontrado\n';
            }
            
            document.getElementById('patternResults').textContent = results;
        }
        
        function showExtractionResults() {
            if (!extractedData) return;
            
            let results = '=== RESULTADOS DE EXTRACCIÓN ===\n\n';
            
            results += `Nombre: "${extractedData.nombre}"\n`;
            results += `Fecha: "${extractedData.fecha}"\n`;
            results += `OrdenRepuestos: "${extractedData.ordenRepuestos}"\n`;
            results += `Cotización: "${extractedData.cotizacion}"\n`;
            results += `Artículos encontrados: ${extractedData.articulos.length}\n\n`;
            
            if (extractedData.articulos.length > 0) {
                results += '=== ARTÍCULOS ===\n';
                extractedData.articulos.forEach((art, index) => {
                    results += `${index + 1}. ${art.descripcion} - $${art.total}\n`;
                });
            }
            
            results += '\n=== TOTALES ===\n';
            results += `SubTotal: $${extractedData.totales.subtotal || 'N/A'}\n`;
            results += `Descuento: $${extractedData.totales.descuento || 'N/A'}\n`;
            results += `Venta Neta: $${extractedData.totales.ventaNeta || 'N/A'}\n`;
            results += `IVA: $${extractedData.totales.iva || 'N/A'}\n`;
            results += `Total: $${extractedData.totales.total || 'N/A'}\n`;
            
            document.getElementById('extractionResults').textContent = results;
        }
        
        // Ejecutar automáticamente al cargar
        window.addEventListener('load', () => {
            setTimeout(debugPDF, 1000);
        });
    </script>
</body>
</html>

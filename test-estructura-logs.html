<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Test Estructura Basada en Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
        .log-structure {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Estructura Basada en Logs</h1>
        <p>Prueba de los patrones corregidos basados en la estructura real observada en los logs del procesamiento.</p>
        
        <div class="success">
            <strong>Estructura Identificada en los Logs:</strong><br>
            <div class="log-structure">
ESTRUCTURA REAL: CODIGO TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION

Ejemplos de los logs:
"OMB8HS-10   722.80 36.14 Detalle 20.00   BUJIAS NGK 13/16 Fuera de Borda"
"OMCPR8EA-9   2,870.20 143.51 Detalle 20.00   BUJIA"
"OMCR7HIX   1,449.00 144.90 Detalle 10.00   BUJIA"
"OMCR7HSA   5,370.30 41.31 Detalle 130.00   BUJIA NGK 5/8 (Moto)"
            </div>
        </div>
        
        <div class="info">
            <strong>Patrones Implementados:</strong><br>
            ✅ Patrón 1: <code>CODIGO_CON_ESPACIO TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION</code><br>
            ✅ Patrón 2: <code>CODIGO_LARGO TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION</code><br>
            ✅ Patrón 3: <code>CODIGO_CORTO TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION</code><br>
            ✅ Patrón 4: <code>CODIGO_FLEXIBLE TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION</code><br>
            ✅ Patrón 5: <code>CODIGO TOTAL PRECIO_UNI TIPO CANTIDAD</code> (sin descripción)<br>
            ✅ Patrón 6: <code>TEXTO TOTAL PRECIO_UNI TIPO CANTIDAD DESCRIPCION</code> (fallback)
        </div>
        
        <button class="btn btn-primary" onclick="testEstructuraLogs()">🚀 Probar Estructura de Logs</button>
        <button class="btn btn-secondary" onclick="testEjemplosLogs()">🧪 Test Ejemplos de Logs</button>
        <button class="btn btn-info" onclick="mostrarLogs()">📋 Ver Logs</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Test</h2>
        <div id="resultadoTest" class="result">Haz clic en "Probar Estructura de Logs" para comenzar...</div>
    </div>

    <div class="container">
        <h2>🧪 Test Ejemplos de Logs</h2>
        <div id="testEjemplos" class="result">Haz clic en "Test Ejemplos de Logs" para probar ejemplos específicos...</div>
    </div>

    <div class="container">
        <h2>🔍 Logs del Procesamiento</h2>
        <div id="logsDetallados" class="result">Los logs aparecerán aquí...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    <script src="js/pdf-exporter.js"></script>
    
    <script>
        // Capturar logs de consola
        const originalLog = console.log;
        const originalError = console.error;
        let consoleLogs = [];

        console.log = function(...args) {
            consoleLogs.push(`${args.join(' ')}`);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleLogs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        async function testEstructuraLogs() {
            let results = '=== TEST ESTRUCTURA BASADA EN LOGS ===\n\n';
            consoleLogs = []; // Limpiar logs
            
            try {
                results += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                if (!response.ok) {
                    throw new Error(`Error cargando PDF: ${response.status}`);
                }
                
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                results += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n`;
                document.getElementById('resultadoTest').textContent = results;
                
                // Procesar con patrones basados en logs
                results += '🔄 Procesando con patrones basados en logs...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Análisis de resultados
                results += '=== RESULTADOS ===\n';
                results += `Error: ${result.error || 'Ninguno'}\n`;
                results += `Archivo: ${result.fileName}\n`;
                results += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    results += '=== DATOS EXTRAÍDOS ===\n';
                    results += `Cliente: "${data.nombre || 'N/A'}"\n`;
                    results += `Fecha: "${data.fecha || 'N/A'}"\n`;
                    results += `OrdenRepuestos: "${data.ordenRepuestos || 'N/A'}"\n`;
                    results += `Artículos: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        results += '🎉 ¡ÉXITO! ESTRUCTURA DE LOGS RECONOCIDA\n\n';
                        results += '=== ARTÍCULOS EXTRAÍDOS ===\n';
                        
                        data.articulos.forEach((art, i) => {
                            results += `${i+1}. "${art.articulo}" - ${art.descripcion} - Cant:${art.cantidad} - Precio:${art.precioUni} - Total:${art.total}\n`;
                        });
                        
                        // Verificar coincidencias con logs
                        results += '\n=== VERIFICACIÓN CON LOGS ===\n';
                        const expectedCodes = ['OMB8HS-10', 'OMCPR8EA-9', 'OMCR7HIX', 'OMCR7HSA', 'OMCR8E', 'OMCR8EIX', 'OMD8EA', 'OMDR8EIX'];
                        const extractedCodes = data.articulos.map(art => art.articulo);
                        
                        expectedCodes.forEach(code => {
                            const found = extractedCodes.includes(code);
                            results += `${found ? '✅' : '❌'} ${code}: ${found ? 'ENCONTRADO' : 'NO ENCONTRADO'}\n`;
                        });
                        
                        // Test de detección de códigos
                        results += '\n=== TEST BOTÓN PDF ===\n';
                        const pdfExporter = new PDFExporter();
                        const hasArticleCodes = pdfExporter.hasArticleCodes([result]);
                        
                        results += `hasArticleCodes: ${hasArticleCodes}\n`;
                        results += `Botón PDF debería aparecer: ${hasArticleCodes ? 'SÍ ✅' : 'NO ❌'}\n\n`;
                        
                        // Contar códigos
                        const codigosEncontrados = data.articulos.filter(art => art.articulo && art.articulo.trim() !== '').length;
                        results += `📊 CÓDIGOS: ${codigosEncontrados}/${data.articulos.length} artículos tienen código\n\n`;
                        
                        results += '=== ESTADO FINAL ===\n';
                        if (data.articulos.length >= 8) {
                            results += '✅ ÉXITO: Se extraen todos los artículos esperados\n';
                        } else {
                            results += `⚠️ PARCIAL: Se extraen ${data.articulos.length}/8 artículos\n`;
                        }
                        
                        if (codigosEncontrados >= 8) {
                            results += '✅ ÉXITO: Se extraen todos los códigos esperados\n';
                        } else {
                            results += `⚠️ PARCIAL: Se extraen ${codigosEncontrados}/8 códigos\n`;
                        }
                        
                        if (hasArticleCodes) {
                            results += '✅ ÉXITO: El botón "Exportar PDF Final" debería aparecer\n';
                        }
                        
                        results += '\n🎯 ¡LA ESTRUCTURA DE LOGS FUNCIONA CORRECTAMENTE!\n';
                        
                    } else {
                        results += '❌ PROBLEMA: Aún no se encontraron artículos\n';
                        results += '🔍 Revisar logs detallados para diagnóstico\n';
                    }
                } else {
                    results += '❌ NO SE EXTRAJERON DATOS\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoTest').textContent = results;
        }

        function testEjemplosLogs() {
            let test = '=== TEST EJEMPLOS DE LOGS ===\n\n';
            
            // Ejemplos exactos de los logs
            const ejemplosLogs = [
                "OMB8HS-10   722.80 36.14 Detalle 20.00   BUJIAS NGK 13/16 Fuera de Borda",
                "OMCPR8EA-9   2,870.20 143.51 Detalle 20.00   BUJIA",
                "OMCR7HIX   1,449.00 144.90 Detalle 10.00   BUJIA",
                "OMCR7HSA   5,370.30 41.31 Detalle 130.00   BUJIA NGK 5/8 (Moto)",
                "OMCR8E   926.20 92.62 Detalle 10.00   BUJIA NGK 5/8 (Moto)",
                "OMCR8EIX   3,613.50 144.54 Detalle 25.00   BUJIA IRIDIUM DE MOTO",
                "OMD8EA   4,505.00 36.04 Detalle 125.00   BUJIA NGK 5/8 (Moto)",
                "OMDR8EIX   4,293.00 143.10 Detalle 30.00   BUJIA"
            ];
            
            test += 'Probando ejemplos exactos de los logs:\n\n';
            
            const processor = new ImprovedPDFProcessor();
            let exitosos = 0;
            
            ejemplosLogs.forEach((ejemplo, i) => {
                test += `Ejemplo ${i+1}: "${ejemplo}"\n`;
                
                try {
                    const resultado = processor.extractFromAutoExcelTableRow(ejemplo, '');
                    if (resultado) {
                        test += `✅ ÉXITO: Código="${resultado.articulo}" Desc="${resultado.descripcion}"\n`;
                        test += `   Cantidad=${resultado.cantidad} Precio=${resultado.precioUni} Total=${resultado.total}\n`;
                        exitosos++;
                    } else {
                        test += `❌ FALLO: No se pudo extraer\n`;
                    }
                } catch (error) {
                    test += `❌ ERROR: ${error.message}\n`;
                }
                test += '\n';
            });
            
            test += `=== RESUMEN ===\n`;
            test += `Exitosos: ${exitosos}/${ejemplosLogs.length}\n`;
            test += `Porcentaje: ${((exitosos/ejemplosLogs.length)*100).toFixed(1)}%\n\n`;
            
            if (exitosos === ejemplosLogs.length) {
                test += '🎉 ¡PERFECTO! Todos los ejemplos de logs funcionan correctamente\n';
            } else {
                test += '⚠️ Algunos ejemplos fallan, revisar patrones\n';
            }
            
            document.getElementById('testEjemplos').textContent = test;
        }

        function mostrarLogs() {
            let logs = '=== LOGS DETALLADOS DEL PROCESAMIENTO ===\n\n';
            
            if (consoleLogs.length === 0) {
                logs += 'No hay logs disponibles. Ejecuta primero "Probar Estructura de Logs".\n';
            } else {
                logs += consoleLogs.join('\n');
            }
            
            document.getElementById('logsDetallados').textContent = logs;
        }

        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(() => {
                testEjemplosLogs();
                setTimeout(testEstructuraLogs, 2000);
            }, 1000);
        });
    </script>
</body>
</html>

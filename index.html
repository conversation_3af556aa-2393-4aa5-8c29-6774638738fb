<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Cotizaciones - Análisis Inteligente de PDFs</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="row bg-primary text-white py-3 mb-4">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    Dashboard de Cotizaciones
                </h1>
                <p class="mb-0 opacity-75">Análisis inteligente de PDFs - 100% Local y Seguro</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="row">
            <!-- Sidebar -->
            <aside class="col-md-3 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Cargar PDFs
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- File Upload Area -->
                        <div id="dropZone" class="drop-zone mb-3">
                            <div class="text-center">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="mb-2">Arrastra tus PDFs aquí</p>
                                <p class="small text-muted">o haz clic para seleccionar</p>
                            </div>
                            <input type="file" id="fileInput" multiple accept=".pdf" class="d-none">
                        </div>

                        <!-- Progress Bar -->
                        <div id="progressContainer" class="d-none mb-3">
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="progressText" class="text-muted">Procesando...</small>
                        </div>

                        <!-- File List -->
                        <div id="fileList" class="file-list"></div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 mt-3">
                            <button id="processBtn" class="btn btn-success" disabled>
                                <i class="fas fa-cogs me-2"></i>
                                Procesar PDFs
                            </button>
                            <button id="exportBtn" class="btn btn-primary" disabled>
                                <i class="fas fa-file-excel me-2"></i>
                                Exportar a Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Estadísticas
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div id="totalFiles" class="stat-number">0</div>
                                    <div class="stat-label">PDFs</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div id="totalClients" class="stat-number">0</div>
                                    <div class="stat-label">Clientes</div>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center mt-2">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div id="totalQuotes" class="stat-number">0</div>
                                    <div class="stat-label">Cotizaciones</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div id="totalItems" class="stat-number">0</div>
                                    <div class="stat-label">Artículos</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Dashboard -->
            <section class="col-md-9">
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <label for="clientFilter" class="form-label">Filtrar por Cliente</label>
                                <select id="clientFilter" class="form-select">
                                    <option value="">Todos los clientes</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="dateFilter" class="form-label">Filtrar por Fecha</label>
                                <input type="date" id="dateFilter" class="form-control">
                            </div>
                            <div class="col-md-4">
                                <label for="searchFilter" class="form-label">Buscar</label>
                                <input type="text" id="searchFilter" class="form-control" placeholder="Buscar en artículos...">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Container -->
                <div id="resultsContainer">
                    <!-- Welcome Message -->
                    <div id="welcomeMessage" class="text-center py-5">
                        <i class="fas fa-file-pdf fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">¡Bienvenido al Dashboard de Cotizaciones!</h4>
                        <p class="text-muted">Carga tus archivos PDF para comenzar el análisis automático</p>
                    </div>

                    <!-- Data Tables -->
                    <div id="dataContainer" class="d-none">
                        <!-- Cotizaciones por Cliente -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    Cotizaciones por Cliente
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="clientsTable" class="table-responsive"></div>
                            </div>
                        </div>

                        <!-- Detalle de Artículos -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Detalle de Artículos
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="itemsTable" class="table-responsive"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <h5>Procesando PDFs...</h5>
                    <p class="text-muted mb-0">Extrayendo información de las cotizaciones</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- SheetJS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Custom Scripts -->
    <script src="config.js"></script>
    <script src="js/pdf-processor.js"></script>
    <script src="js/excel-exporter.js"></script>
    <script src="js/app.js"></script>
</body>
</html>

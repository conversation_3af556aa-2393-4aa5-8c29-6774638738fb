# 🚀 Instrucciones Rápidas - Dashboard de Cotizaciones

## ⚡ Inicio Rápido (2 minutos)

### 1. Abrir la Aplicación
- **Doble clic** en `index.html`
- Se abrirá automáticamente en tu navegador

### 2. Cargar PDFs
- **Arrastra** tus archivos PDF a la zona azul
- O **haz clic** en la zona para seleccionar archivos

### 3. Procesar
- Clic en **"Procesar PDFs"**
- Espera a que termine (barra de progreso)

### 4. Exportar
- Clic en **"Exportar a Excel"**
- El archivo se descarga automáticamente

## 📋 Checklist Pre-Uso

- [ ] Navegador moderno (Chrome, Firefox, Edge)
- [ ] JavaScript habilitado
- [ ] Conexión a internet (para cargar librerías)
- [ ] PDFs con texto (no solo imágenes escaneadas)

## 🎯 Qué Extrae la Aplicación

### ✅ Datos que SÍ detecta bien:
- Nombres de clientes en formatos estándar
- Fechas en formato DD/MM/YYYY o DD-MM-YYYY
- Tablas con cantidad, descripción y precio
- Totales y subtotales claramente marcados
- Números de cotización/folio

### ⚠️ Datos que pueden necesitar ajuste:
- Formatos de fecha muy específicos
- Tablas con diseños no estándar
- Clientes con nombres muy largos o complejos
- PDFs con mucho texto decorativo

## 📊 Reportes Generados

El Excel incluye **5 hojas**:

1. **Resumen** - Estadísticas generales
2. **Cotizaciones** - Lista completa
3. **Artículos** - Detalle de productos
4. **Clientes** - Análisis por cliente
5. **Estadísticas** - Tendencias y top productos

## 🔧 Solución Rápida de Problemas

### "No se detectan artículos"
- Verificar que el PDF tiene tablas claras
- Revisar que los precios tengan formato $X,XXX.XX

### "Cliente no identificado"
- Buscar líneas como "Cliente:", "Para:", "Empresa:"
- Verificar que el nombre esté en las primeras páginas

### "Error al procesar PDF"
- Verificar que no tenga contraseña
- Intentar con un PDF más simple primero

### "No se puede exportar"
- Verificar permisos de descarga del navegador
- Intentar con otro navegador

## 📞 Soporte Rápido

1. **Abrir consola del navegador** (F12)
2. **Buscar errores** en la pestaña "Console"
3. **Tomar captura** del error
4. **Probar con PDF de ejemplo** para confirmar funcionamiento

## 🎨 Personalización Básica

### Cambiar patrones de cliente:
Editar `js/pdf-processor.js` línea ~15:
```javascript
cliente: [
    /(?:cliente|client|empresa|company)[\s:]+([^\n\r]+)/i,
    // Agregar tu patrón aquí
]
```

### Cambiar patrones de fecha:
```javascript
fecha: [
    /(?:fecha|date)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
    // Agregar tu patrón aquí
]
```

## 🔒 Seguridad

- ✅ **100% Local** - No envía datos a internet
- ✅ **Sin instalación** - No modifica tu sistema
- ✅ **Sin almacenamiento** - No guarda datos permanentemente
- ✅ **Compatible IT** - Funciona en entornos corporativos

## 📈 Mejores Prácticas

### Para mejores resultados:
1. **PDFs con texto** (no escaneados)
2. **Formato estándar** de cotización
3. **Archivos menores a 10MB** cada uno
4. **Máximo 50 PDFs** por lote

### Preparar PDFs:
- Verificar que se puede seleccionar texto
- Revisar que las tablas estén bien formateadas
- Confirmar que los totales sean visibles

## 🎯 Casos de Uso Típicos

### ✅ Funciona excelente para:
- Cotizaciones de proveedores estándar
- Facturas con formato tradicional
- Presupuestos de construcción
- Listas de precios con tablas

### ⚠️ Puede necesitar ajustes para:
- Diseños muy creativos o artísticos
- PDFs con muchas imágenes
- Formatos completamente personalizados
- Documentos en otros idiomas

---

## 🎉 ¡Listo para usar!

**Tiempo estimado de configuración: 0 minutos**
**Tiempo de procesamiento: 1-2 segundos por PDF**
**Tiempo de exportación: 5-10 segundos**

### 📞 ¿Necesitas ayuda?
1. Revisar README.md completo
2. Verificar data-example.json para ejemplos
3. Probar con PDFs simples primero
4. Contactar soporte técnico si persisten problemas

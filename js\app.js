/**
 * Main Application Controller
 * Handles UI interactions and coordinates between modules
 */

class CotizacionesApp {
    constructor() {
        // Use improved processor if available, fallback to original
        this.pdfProcessor = window.ImprovedPDFProcessor ? new ImprovedPDFProcessor() : new PDFProcessor();
        this.excelExporter = new ExcelExporter();
        this.processedData = [];
        this.selectedFiles = [];

        this.initializeEventListeners();
        this.updateStatistics();

        // Show which processor is being used
        console.log('Usando procesador:', this.pdfProcessor.constructor.name);
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        // File input and drop zone
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        
        dropZone.addEventListener('click', () => fileInput.click());
        dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
        dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        dropZone.addEventListener('drop', this.handleDrop.bind(this));
        
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Action buttons
        document.getElementById('processBtn').addEventListener('click', this.processFiles.bind(this));
        document.getElementById('exportBtn').addEventListener('click', this.exportToExcel.bind(this));
        
        // Filters
        document.getElementById('clientFilter').addEventListener('change', this.applyFilters.bind(this));
        document.getElementById('dateFilter').addEventListener('change', this.applyFilters.bind(this));
        document.getElementById('searchFilter').addEventListener('input', this.applyFilters.bind(this));
    }

    /**
     * Handle drag over event
     * @param {Event} e 
     */
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('dropZone').classList.add('dragover');
    }

    /**
     * Handle drag leave event
     * @param {Event} e 
     */
    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('dropZone').classList.remove('dragover');
    }

    /**
     * Handle drop event
     * @param {Event} e 
     */
    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('dropZone').classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
        );
        
        if (files.length > 0) {
            this.addFiles(files);
        } else {
            this.showAlert('Por favor, selecciona solo archivos PDF.', 'warning');
        }
    }

    /**
     * Handle file selection
     * @param {Event} e 
     */
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.addFiles(files);
    }

    /**
     * Add files to the processing queue
     * @param {Array} files 
     */
    addFiles(files) {
        files.forEach(file => {
            if (!this.selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                this.selectedFiles.push(file);
            }
        });
        
        this.updateFileList();
        this.updateProcessButton();
    }

    /**
     * Update file list display
     */
    updateFileList() {
        const fileList = document.getElementById('fileList');
        
        if (this.selectedFiles.length === 0) {
            fileList.innerHTML = '<p class="text-muted small">No hay archivos seleccionados</p>';
            return;
        }
        
        fileList.innerHTML = this.selectedFiles.map((file, index) => `
            <div class="file-item" data-index="${index}">
                <div class="file-info">
                    <i class="fas fa-file-pdf file-icon"></i>
                    <div>
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${this.formatFileSize(file.size)}</div>
                    </div>
                </div>
                <div class="file-status">
                    <span class="status-badge status-pending">Pendiente</span>
                    <button class="btn btn-sm btn-outline-danger ms-2" onclick="app.removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Remove file from queue
     * @param {number} index 
     */
    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.updateFileList();
        this.updateProcessButton();
    }

    /**
     * Update process button state
     */
    updateProcessButton() {
        const processBtn = document.getElementById('processBtn');
        processBtn.disabled = this.selectedFiles.length === 0;
    }

    /**
     * Process all selected files
     */
    async processFiles() {
        if (this.selectedFiles.length === 0) return;

        // Timeout de seguridad para cerrar el modal
        const safetyTimeout = setTimeout(() => {
            console.warn('⚠️ Timeout de seguridad: cerrando modal automáticamente');
            this.showLoadingModal(false);
        }, 30000); // 30 segundos

        try {
            console.log(`Iniciando procesamiento de ${this.selectedFiles.length} archivos...`);
            this.showLoadingModal(true);
            this.updateProcessProgress(0, this.selectedFiles.length, 'Iniciando procesamiento...');

            // Show which processor is being used
            this.showAlert(
                `Usando procesador: ${this.pdfProcessor.constructor.name}`,
                'info'
            );

            const results = await this.pdfProcessor.processMultiplePDFs(
                this.selectedFiles,
                this.handleProcessProgress.bind(this)
            );

            console.log('Resultados del procesamiento:', results);

            this.processedData = results;

            // Asegurar que el modal se cierre antes de actualizar la UI
            this.showLoadingModal(false);

            this.updateDataDisplay();
            this.updateStatistics();
            this.populateFilters();

            // Enable export button
            document.getElementById('exportBtn').disabled = false;

            const successCount = results.filter(r => !r.error).length;
            const errorCount = results.length - successCount;

            let message = `Procesamiento completado. ${successCount} de ${results.length} archivos procesados exitosamente.`;
            if (errorCount > 0) {
                message += ` ${errorCount} archivos tuvieron errores.`;
            }

            this.showAlert(message, successCount > 0 ? 'success' : 'warning');

            // Log detailed results
            results.forEach(result => {
                if (result.error) {
                    console.error(`Error en ${result.fileName}:`, result.error);
                } else {
                    console.log(`Éxito en ${result.fileName}:`, {
                        cliente: result.extractedData?.cliente,
                        articulos: result.extractedData?.articulos?.length,
                        tiempo: result.processingTime
                    });
                }
            });

        } catch (error) {
            console.error('Error processing files:', error);
            this.showAlert('Error durante el procesamiento: ' + error.message, 'danger');
        } finally {
            // Limpiar timeout de seguridad
            clearTimeout(safetyTimeout);

            // Asegurar que el modal se cierre
            console.log('🔄 Cerrando modal en finally...');
            this.showLoadingModal(false);

            // Timeout adicional para asegurar cierre
            setTimeout(() => {
                console.log('🔄 Verificación final de cierre de modal...');
                this.showLoadingModal(false);
            }, 1000);
        }
    }

    /**
     * Handle processing progress updates
     * @param {Object} progress 
     */
    handleProcessProgress(progress) {
        this.updateProcessProgress(progress.current, progress.total, progress.fileName);
        this.updateFileStatus(progress.fileName, progress.status, progress.error);
    }

    /**
     * Update process progress display
     * @param {number} current 
     * @param {number} total 
     * @param {string} fileName 
     */
    updateProcessProgress(current, total, fileName) {
        const percentage = Math.round((current / total) * 100);
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `Procesando: ${fileName} (${current}/${total})`;
        }
    }

    /**
     * Update file status in the list
     * @param {string} fileName 
     * @param {string} status 
     * @param {string} error 
     */
    updateFileStatus(fileName, status, error) {
        const fileItems = document.querySelectorAll('.file-item');
        
        fileItems.forEach(item => {
            const fileNameElement = item.querySelector('.file-name');
            if (fileNameElement && fileNameElement.textContent === fileName) {
                const statusBadge = item.querySelector('.status-badge');
                
                statusBadge.className = 'status-badge';
                
                switch (status) {
                    case 'processing':
                        statusBadge.classList.add('status-processing');
                        statusBadge.textContent = 'Procesando...';
                        break;
                    case 'completed':
                        statusBadge.classList.add('status-success');
                        statusBadge.textContent = 'Completado';
                        break;
                    case 'error':
                        statusBadge.classList.add('status-error');
                        statusBadge.textContent = 'Error';
                        statusBadge.title = error || 'Error desconocido';
                        break;
                }
            }
        });
    }

    /**
     * Update data display tables
     */
    updateDataDisplay() {
        const validData = this.processedData.filter(item => !item.error);
        
        if (validData.length === 0) {
            document.getElementById('welcomeMessage').classList.remove('d-none');
            document.getElementById('dataContainer').classList.add('d-none');
            return;
        }
        
        document.getElementById('welcomeMessage').classList.add('d-none');
        document.getElementById('dataContainer').classList.remove('d-none');
        
        this.updateClientsTable(validData);
        this.updateItemsTable(validData);
    }

    /**
     * Update clients table
     * @param {Array} data 
     */
    updateClientsTable(data) {
        const clientsTable = document.getElementById('clientsTable');
        const clientStats = this.getClientStats(data);
        
        const tableHTML = `
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Cliente</th>
                        <th>Cotizaciones</th>
                        <th>Artículos</th>
                        <th>Monto Total</th>
                        <th>Última Fecha</th>
                    </tr>
                </thead>
                <tbody>
                    ${clientStats.map(stat => `
                        <tr>
                            <td><strong>${stat.cliente}</strong></td>
                            <td><span class="badge bg-primary">${stat.cotizaciones}</span></td>
                            <td>${stat.articulos}</td>
                            <td><strong>$${stat.total.toLocaleString('es-ES', {minimumFractionDigits: 2})}</strong></td>
                            <td>${stat.ultimaFecha}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        clientsTable.innerHTML = tableHTML;
    }

    /**
     * Update items table
     * @param {Array} data 
     */
    updateItemsTable(data) {
        const itemsTable = document.getElementById('itemsTable');
        const allItems = [];
        
        data.forEach(item => {
            const extracted = item.extractedData || {};
            const articulos = extracted.articulos || [];
            
            articulos.forEach(articulo => {
                allItems.push({
                    archivo: item.fileName,
                    cliente: extracted.nombre || extracted.cliente || 'N/A',
                    fecha: extracted.fecha || 'N/A',
                    ordenRepuestos: extracted.ordenRepuestos || 'N/A',
                    ...articulo
                });
            });
        });
        
        const tableHTML = `
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Cliente</th>
                        <th>Vehículo</th>
                        <th>OrdenRepuestos</th>
                        <th>Código</th>
                        <th>Descripción</th>
                        <th>T. Facturación</th>
                        <th>Existencia Nacional</th>
                        <th>Cantidad Solicitada</th>
                        <th>Fecha Solicitud</th>
                    </tr>
                </thead>
                <tbody>
                    ${allItems.slice(0, 100).map(item => `
                        <tr>
                            <td>${item.cliente}</td>
                            <td class="text-muted">-</td>
                            <td><span class="badge bg-info">${item.ordenRepuestos !== 'Orden no identificada' ? item.ordenRepuestos : 'N/A'}</span></td>
                            <td>${item.articulo && item.articulo !== 'N/A' ? item.articulo : '<span class="text-muted">-</span>'}</td>
                            <td class="text-truncate-2">${item.descripcion || 'N/A'}</td>
                            <td><span class="badge bg-primary">${item.tFacturacion || 'N/A'}</span></td>
                            <td class="text-muted">-</td>
                            <td>${item.cantidad || 1}</td>
                            <td>${item.fecha}</td>
                        </tr>
                    `).join('')}
                    ${allItems.length > 100 ? `
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <em>Mostrando los primeros 100 artículos de ${allItems.length} total.
                                Exporta a Excel para ver todos los datos.</em>
                            </td>
                        </tr>
                    ` : ''}
                </tbody>
            </table>
        `;
        
        itemsTable.innerHTML = tableHTML;
    }

    /**
     * Get client statistics
     * @param {Array} data 
     * @returns {Array}
     */
    getClientStats(data) {
        const clientMap = new Map();
        
        data.forEach(item => {
            const cliente = item.extractedData?.nombre || item.extractedData?.cliente || 'Cliente no identificado';
            const total = item.extractedData?.totales?.total || 0;
            const articulos = item.extractedData?.articulos?.length || 0;
            const fecha = item.extractedData?.fecha || '';
            
            if (!clientMap.has(cliente)) {
                clientMap.set(cliente, {
                    cliente,
                    cotizaciones: 0,
                    total: 0,
                    articulos: 0,
                    fechas: []
                });
            }
            
            const stat = clientMap.get(cliente);
            stat.cotizaciones++;
            stat.total += total;
            stat.articulos += articulos;
            stat.fechas.push(fecha);
        });
        
        return Array.from(clientMap.values()).map(stat => ({
            ...stat,
            ultimaFecha: stat.fechas.sort().pop() || 'N/A'
        })).sort((a, b) => b.total - a.total);
    }

    /**
     * Update statistics display
     */
    updateStatistics() {
        const validData = this.processedData.filter(item => !item.error);
        const totalFiles = this.processedData.length;
        const totalClients = new Set(validData.map(item => item.extractedData?.nombre || item.extractedData?.cliente)).size;
        const totalQuotes = validData.length;
        const totalItems = validData.reduce((sum, item) => 
            sum + (item.extractedData?.articulos?.length || 0), 0);
        
        document.getElementById('totalFiles').textContent = totalFiles;
        document.getElementById('totalClients').textContent = totalClients;
        document.getElementById('totalQuotes').textContent = totalQuotes;
        document.getElementById('totalItems').textContent = totalItems;
    }

    /**
     * Populate filter dropdowns
     */
    populateFilters() {
        const validData = this.processedData.filter(item => !item.error);
        const clients = [...new Set(validData.map(item => item.extractedData?.nombre || item.extractedData?.cliente))].filter(Boolean);
        
        const clientFilter = document.getElementById('clientFilter');
        clientFilter.innerHTML = '<option value="">Todos los clientes</option>';
        
        clients.forEach(client => {
            const option = document.createElement('option');
            option.value = client;
            option.textContent = client;
            clientFilter.appendChild(option);
        });
    }

    /**
     * Apply filters to data display
     */
    applyFilters() {
        // This would filter the displayed data based on selected filters
        // Implementation depends on specific filtering requirements
        console.log('Applying filters...');
    }

    /**
     * Export data to Excel
     */
    async exportToExcel() {
        if (this.processedData.length === 0) {
            this.showAlert('No hay datos para exportar.', 'warning');
            return;
        }
        
        try {
            const fileName = `reporte-cotizaciones-${new Date().toISOString().split('T')[0]}.xlsx`;
            await this.excelExporter.exportToExcel(this.processedData, fileName);
            this.showAlert('Archivo Excel generado exitosamente.', 'success');
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            this.showAlert('Error al generar archivo Excel: ' + error.message, 'danger');
        }
    }

    /**
     * Show/hide loading modal
     * @param {boolean} show
     */
    showLoadingModal(show) {
        console.log(`📱 showLoadingModal(${show})`);
        const modal = document.getElementById('loadingModal');

        if (show) {
            console.log('📱 Mostrando modal...');
            // Crear nueva instancia solo si no existe
            if (!this.loadingModalInstance) {
                console.log('📱 Creando nueva instancia de modal');
                this.loadingModalInstance = new bootstrap.Modal(modal, {
                    backdrop: 'static',
                    keyboard: false
                });
            }
            this.loadingModalInstance.show();
            console.log('📱 Modal mostrado');
        } else {
            console.log('📱 Cerrando modal...');

            // Cerrar modal existente
            if (this.loadingModalInstance) {
                console.log('📱 Cerrando instancia existente');
                this.loadingModalInstance.hide();
                // Limpiar instancia después de un delay
                setTimeout(() => {
                    if (this.loadingModalInstance) {
                        console.log('📱 Limpiando instancia de modal');
                        this.loadingModalInstance.dispose();
                        this.loadingModalInstance = null;
                    }
                }, 500);
            }

            // Fallback: forzar cierre manual
            console.log('📱 Aplicando cierre manual de fallback');
            modal.classList.remove('show');
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');

            // Remover backdrop si existe
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                console.log('📱 Removiendo backdrop');
                backdrop.remove();
            }

            console.log('📱 Modal cerrado completamente');
        }
    }

    /**
     * Show alert message
     * @param {string} message 
     * @param {string} type 
     */
    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of main content
        const main = document.querySelector('main');
        main.insertBefore(alertDiv, main.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    /**
     * Format file size for display
     * @param {number} bytes 
     * @returns {string}
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CotizacionesApp();
});

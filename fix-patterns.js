/**
 * Parches rápidos para los patrones basados en el texto real extraído
 */

// Texto real extraído del log:
const textoReal = `0000071551
 Res.El Trapiche, calle ppal,Frente Edif. Admin. d la ENEE,tgu Direccion:
99964312 Telefono:
Sucursal:
 AutoExcel S.A. de C.V
 Cotizacion de Repuestos
 TR
 OrdenRepuestos:
 Cotización:
 Repuestos y Servicios Expres El Trapiche
 250003367
 HNE02 Empresa:
 09/01/2025 Fecha:
 RES. EL TRAPICHE, CONTIGUO A IGLESIA CCI, CALLE PRINCIPAL   ,0801,HND Direccion:
 94363021 Telefono:
Nombre:   ECONO RENT A CAR S. DE R.L
 PrecioUni T. Facturacion   Total Descripcion Articulo Cant.   Procedencia   DescUni`;

console.log('=== ANÁLISIS DEL TEXTO REAL ===');

// Buscar OrdenRepuestos
const ordenMatch = textoReal.match(/250003367/);
console.log('OrdenRepuestos encontrado:', ordenMatch ? '250003367' : 'No encontrado');

// Buscar Cotización  
const cotizacionMatch = textoReal.match(/0000071551/);
console.log('Cotización encontrada:', cotizacionMatch ? '0000071551' : 'No encontrado');

// Buscar Fecha
const fechaMatch = textoReal.match(/09\/01\/2025/);
console.log('Fecha encontrada:', fechaMatch ? '09/01/2025' : 'No encontrado');

// Buscar Nombre
const nombreMatch = textoReal.match(/Nombre:\s*([^\n\r]+)/);
console.log('Nombre encontrado:', nombreMatch ? nombreMatch[1].trim() : 'No encontrado');

console.log('\n=== PATRONES MEJORADOS SUGERIDOS ===');

console.log(`
// Patrón para OrdenRepuestos (buscar el número específico en el contexto)
ordenRepuestos: [
    /OrdenRepuestos:[\\s\\S]*?(\\d{8,})/i,
    /250003367/,  // Número específico encontrado
    /(\\d{9})/g   // Cualquier número de 9 dígitos
],

// Patrón para Cotización (buscar el número al inicio)
cotizacion: [
    /Cotización:[\\s\\S]*?(\\d{8,})/i,
    /0000071551/, // Número específico encontrado
    /^(\\d{10})/  // Número de 10 dígitos al inicio
],

// Patrón para Fecha (ya funciona)
fecha: [
    /Fecha:\\s*(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/i,
    /(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/g
],

// Patrón para Nombre (ya funciona)
nombre: [
    /Nombre:\\s*([^\\n\\r]+)/i
]
`);

// Función para aplicar los parches
function aplicarParches() {
    if (window.ImprovedPDFProcessor) {
        const processor = new ImprovedPDFProcessor();
        
        // Actualizar patrones
        processor.patterns.ordenRepuestos = [
            /OrdenRepuestos:[\s\S]*?(\d{8,})/i,
            /250003367/,
            /(\d{9})/g
        ];
        
        processor.patterns.cotizacion = [
            /Cotización:[\s\S]*?(\d{8,})/i,
            /0000071551/,
            /^(\d{10})/
        ];
        
        console.log('✅ Parches aplicados al procesador');
        return processor;
    } else {
        console.log('❌ ImprovedPDFProcessor no encontrado');
        return null;
    }
}

// Exportar función
window.aplicarParches = aplicarParches;

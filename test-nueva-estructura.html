<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Test Nueva Estructura - Verificación de Columnas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        table { font-size: 11px; }
        th { background-color: #007bff; color: white; }
        .badge { font-size: 10px; }
        .text-muted { color: #6c757d !important; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Test Nueva Estructura - Verificación de Columnas</h1>
        <p>Prueba para verificar que la tabla muestre las columnas en el orden correcto:</p>
        
        <div class="info">
            <strong>Orden Solicitado:</strong><br>
            Cliente | Vehículo | Código | Descripción | T. Facturación | Existencia Nacional | Cantidad Solicitada | Fecha Solicitud
        </div>
        
        <button class="btn btn-primary" onclick="testEstructura()">🚀 Probar Nueva Estructura</button>
        <button class="btn btn-success" onclick="mostrarEjemplo()">📋 Mostrar Ejemplo</button>
    </div>

    <div class="container">
        <h2>📊 Tabla de Prueba</h2>
        <div id="tablaTest" class="table-responsive">Haz clic en "Probar Nueva Estructura" para ver la tabla...</div>
    </div>

    <div class="container">
        <h2>📄 Estructura Excel</h2>
        <div id="estructuraExcel" class="result">Esperando test...</div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testEstructura() {
            // Datos de ejemplo
            const datosEjemplo = [
                {
                    cliente: 'INTERNATIONAL BUSINESS S.A. DE C.V.',
                    vehiculo: '',
                    codigo: 'FAROL',
                    descripcion: 'FAROL DELANTERO RH HALOGENO',
                    tFacturacion: 'Detalle',
                    existenciaNacional: '',
                    cantidadSolicitada: '1.00',
                    fechaSolicitud: '2025-01-06'
                },
                {
                    cliente: 'ECONO RENT A CAR S. DE R.L',
                    vehiculo: '',
                    codigo: 'FILTRO',
                    descripcion: 'FILTRO DE AIRE (C)',
                    tFacturacion: 'Detalle',
                    existenciaNacional: '',
                    cantidadSolicitada: '2.00',
                    fechaSolicitud: '2025-01-09'
                }
            ];
            
            mostrarTabla(datosEjemplo);
            mostrarEstructuraExcel();
        }
        
        function mostrarTabla(datos) {
            const tableHTML = `
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Cliente</th>
                            <th>Vehículo</th>
                            <th>Código</th>
                            <th>Descripción</th>
                            <th>T. Facturación</th>
                            <th>Existencia Nacional</th>
                            <th>Cantidad Solicitada</th>
                            <th>Fecha Solicitud</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${datos.map(item => `
                            <tr>
                                <td>${item.cliente}</td>
                                <td class="text-muted">-</td>
                                <td>${item.codigo}</td>
                                <td class="text-truncate-2">${item.descripcion}</td>
                                <td><span class="badge bg-primary">${item.tFacturacion}</span></td>
                                <td class="text-muted">-</td>
                                <td>${item.cantidadSolicitada}</td>
                                <td>${item.fechaSolicitud}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('tablaTest').innerHTML = tableHTML;
        }
        
        function mostrarEstructuraExcel() {
            const estructura = `
=== ESTRUCTURA EXCEL - HOJA "ARTÍCULOS" ===

Columnas en orden:
1. ARCHIVO
2. CLIENTE
3. VEHÍCULO (en blanco)
4. CÓDIGO
5. DESCRIPCIÓN
6. T.FACTURACIÓN
7. EXISTENCIA NACIONAL (en blanco)
8. CANTIDAD SOLICITADA
9. FECHA SOLICITUD
10. PRECIO UNI
11. DESC UNI
12. TOTAL

Ejemplo de fila:
business 6.pdf | INTERNATIONAL BUSINESS S.A. DE C.V. | | FAROL | FAROL DELANTERO RH HALOGENO | Detalle | | 1.00 | 2025-01-06 | 22885.28 | 8009.85 | 14875.43

✅ VERIFICACIONES:
- Vehículo y Existencia Nacional aparecen como columnas vacías
- Cantidad se renombra a "Cantidad Solicitada"
- Fecha se renombra a "Fecha Solicitud"
- T. Facturación mantiene su posición
- Código usa el campo "articulo" del procesador
            `;
            
            document.getElementById('estructuraExcel').textContent = estructura;
        }
        
        function mostrarEjemplo() {
            const ejemplo = `
=== EJEMPLO DE TABLA FINAL ===

┌─────────────────────────────────────┬──────────┬────────┬─────────────────────────────┬───────────────┬───────────────────┬───────────────────┬─────────────────┐
│ Cliente                             │ Vehículo │ Código │ Descripción                 │ T. Facturación│ Existencia Nac.   │ Cantidad Solicit. │ Fecha Solicitud │
├─────────────────────────────────────┼──────────┼────────┼─────────────────────────────┼───────────────┼───────────────────┼───────────────────┼─────────────────┤
│ INTERNATIONAL BUSINESS S.A. DE C.V. │    -     │ FAROL  │ FAROL DELANTERO RH HALOGENO │   [Detalle]   │        -          │       1.00        │   2025-01-06    │
│ ECONO RENT A CAR S. DE R.L          │    -     │ FILTRO │ FILTRO DE AIRE (C)          │   [Detalle]   │        -          │       2.00        │   2025-01-09    │
└─────────────────────────────────────┴──────────┴────────┴─────────────────────────────┴───────────────┴───────────────────┴───────────────────┴─────────────────┘

NOTAS:
- Las columnas "Vehículo" y "Existencia Nacional" aparecen con "-" (en gris)
- "T. Facturación" se muestra como badge azul
- "Código" usa el primer campo del artículo extraído
- Los nombres de columnas son exactos a lo solicitado
            `;
            
            document.getElementById('estructuraExcel').textContent = ejemplo;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testEstructura, 1000);
        });
    </script>
</body>
</html>

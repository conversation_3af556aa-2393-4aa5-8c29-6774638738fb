<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Códigos Mixtos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
        .code-examples {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Códigos Mixtos</h1>
        <p>Prueba de los patrones actualizados para reconocer códigos mixtos en diferentes formatos.</p>
        
        <div class="success">
            <strong>Tipos de Códigos Soportados:</strong><br>
            <div class="code-examples">
✅ Códigos cortos: OMD8EA, OMB8HS-10, OMCR7HIX
✅ Códigos largos: 15400PLMA01, 90915YZZD4
✅ Códigos con espacio: MB MZ690411, AC PF2129
✅ Códigos con guión: ABC-123, XYZ-456-789
            </div>
        </div>
        
        <div class="info">
            <strong>Patrones Implementados:</strong><br>
            1. <code>Códigos con espacio</code> (ej: "MB MZ690411")<br>
            2. <code>Códigos alfanuméricos largos</code> (ej: "15400PLMA01")<br>
            3. <code>Códigos cortos</code> (ej: "OMD8EA")<br>
            4. <code>Códigos flexibles</code> (cualquier combinación)<br>
            5. <code>Sin total</code> (variante sin precio total)<br>
            6. <code>Fallback</code> (sin código específico)
        </div>
        
        <button class="btn btn-primary" onclick="testCodigosMixtos()">🚀 Probar Códigos Mixtos</button>
        <button class="btn btn-secondary" onclick="testPatronesManual()">🧪 Test Manual de Patrones</button>
        <button class="btn btn-info" onclick="mostrarLogs()">📋 Ver Logs</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Test</h2>
        <div id="resultadoTest" class="result">Haz clic en "Probar Códigos Mixtos" para comenzar...</div>
    </div>

    <div class="container">
        <h2>🧪 Test Manual de Patrones</h2>
        <div id="testManual" class="result">Haz clic en "Test Manual de Patrones" para probar ejemplos...</div>
    </div>

    <div class="container">
        <h2>🔍 Logs del Procesamiento</h2>
        <div id="logsDetallados" class="result">Los logs aparecerán aquí...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    <script src="js/pdf-exporter.js"></script>
    
    <script>
        // Capturar logs de consola
        const originalLog = console.log;
        const originalError = console.error;
        let consoleLogs = [];

        console.log = function(...args) {
            consoleLogs.push(`${args.join(' ')}`);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleLogs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        async function testCodigosMixtos() {
            let results = '=== TEST CÓDIGOS MIXTOS ===\n\n';
            consoleLogs = []; // Limpiar logs
            
            try {
                results += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                if (!response.ok) {
                    throw new Error(`Error cargando PDF: ${response.status}`);
                }
                
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                results += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n`;
                document.getElementById('resultadoTest').textContent = results;
                
                // Procesar con patrones para códigos mixtos
                results += '🔄 Procesando con patrones para códigos mixtos...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Análisis de resultados
                results += '=== RESULTADOS ===\n';
                results += `Error: ${result.error || 'Ninguno'}\n`;
                results += `Archivo: ${result.fileName}\n`;
                results += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    results += '=== DATOS EXTRAÍDOS ===\n';
                    results += `Cliente: "${data.nombre || 'N/A'}"\n`;
                    results += `Fecha: "${data.fecha || 'N/A'}"\n`;
                    results += `OrdenRepuestos: "${data.ordenRepuestos || 'N/A'}"\n`;
                    results += `Artículos: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        results += '🎉 ¡ÉXITO! CÓDIGOS MIXTOS RECONOCIDOS\n\n';
                        results += '=== ANÁLISIS DE CÓDIGOS ===\n';
                        
                        const tiposCodigos = {
                            cortos: [],
                            largos: [],
                            conEspacio: [],
                            conGuion: [],
                            sinCodigo: []
                        };
                        
                        data.articulos.forEach((art, i) => {
                            const codigo = art.articulo;
                            results += `${i+1}. "${codigo}" - ${art.descripcion}\n`;
                            
                            // Clasificar tipos de códigos
                            if (!codigo || codigo.trim() === '') {
                                tiposCodigos.sinCodigo.push(art);
                            } else if (codigo.includes(' ')) {
                                tiposCodigos.conEspacio.push(art);
                            } else if (codigo.includes('-')) {
                                tiposCodigos.conGuion.push(art);
                            } else if (codigo.length >= 8) {
                                tiposCodigos.largos.push(art);
                            } else {
                                tiposCodigos.cortos.push(art);
                            }
                        });
                        
                        results += '\n=== CLASIFICACIÓN DE CÓDIGOS ===\n';
                        results += `Códigos cortos (3-7 chars): ${tiposCodigos.cortos.length}\n`;
                        results += `Códigos largos (8+ chars): ${tiposCodigos.largos.length}\n`;
                        results += `Códigos con espacio: ${tiposCodigos.conEspacio.length}\n`;
                        results += `Códigos con guión: ${tiposCodigos.conGuion.length}\n`;
                        results += `Sin código: ${tiposCodigos.sinCodigo.length}\n\n`;
                        
                        // Test de detección de códigos
                        results += '=== TEST BOTÓN PDF ===\n';
                        const pdfExporter = new PDFExporter();
                        const hasArticleCodes = pdfExporter.hasArticleCodes([result]);
                        
                        results += `hasArticleCodes: ${hasArticleCodes}\n`;
                        results += `Botón PDF debería aparecer: ${hasArticleCodes ? 'SÍ ✅' : 'NO ❌'}\n\n`;
                        
                        // Contar códigos
                        const codigosEncontrados = data.articulos.filter(art => art.articulo && art.articulo.trim() !== '').length;
                        results += `📊 CÓDIGOS: ${codigosEncontrados}/${data.articulos.length} artículos tienen código\n\n`;
                        
                        results += '=== ESTADO FINAL ===\n';
                        results += '✅ ÉXITO: Los patrones reconocen códigos mixtos\n';
                        results += '✅ ÉXITO: Se extraen artículos correctamente\n';
                        if (hasArticleCodes) {
                            results += '✅ ÉXITO: El botón "Exportar PDF Final" debería aparecer\n';
                        }
                        results += '\n🎯 ¡CÓDIGOS MIXTOS SOPORTADOS CORRECTAMENTE!\n';
                        
                    } else {
                        results += '❌ PROBLEMA: Aún no se encontraron artículos\n';
                        results += '🔍 Revisar logs detallados para diagnóstico\n';
                    }
                } else {
                    results += '❌ NO SE EXTRAJERON DATOS\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoTest').textContent = results;
        }

        function testPatronesManual() {
            let test = '=== TEST MANUAL DE PATRONES ===\n\n';
            
            // Ejemplos de líneas con diferentes tipos de códigos
            const ejemplos = [
                "20.00 OMB8HS-10 BUJIAS NGK 13/16 Fuera de Borda Detalle 36.14 722.80",
                "1.00 15400PLMA01 FILTRO DE ACEITE HONDA Detalle 25.50 25.50",
                "2.00 MB MZ690411 PASTILLAS DE FRENO MITSUBISHI Detalle 85.00 170.00",
                "5.00 AC-PF2129 FILTRO DE COMBUSTIBLE AC DELCO Detalle 12.75 63.75",
                "10.00 NGK7092 BUJIA NGK IRIDIUM Detalle 15.20 152.00"
            ];
            
            test += 'Probando ejemplos de códigos mixtos:\n\n';
            
            const processor = new ImprovedPDFProcessor();
            
            ejemplos.forEach((ejemplo, i) => {
                test += `Ejemplo ${i+1}: "${ejemplo}"\n`;
                
                try {
                    const resultado = processor.extractFromAutoExcelTableRow(ejemplo, '');
                    if (resultado) {
                        test += `✅ ÉXITO: Código="${resultado.articulo}" Desc="${resultado.descripcion}"\n`;
                        test += `   Cantidad=${resultado.cantidad} Precio=${resultado.precioUni} Total=${resultado.total}\n`;
                    } else {
                        test += `❌ FALLO: No se pudo extraer\n`;
                    }
                } catch (error) {
                    test += `❌ ERROR: ${error.message}\n`;
                }
                test += '\n';
            });
            
            document.getElementById('testManual').textContent = test;
        }

        function mostrarLogs() {
            let logs = '=== LOGS DETALLADOS DEL PROCESAMIENTO ===\n\n';
            
            if (consoleLogs.length === 0) {
                logs += 'No hay logs disponibles. Ejecuta primero "Probar Códigos Mixtos".\n';
            } else {
                logs += consoleLogs.join('\n');
            }
            
            document.getElementById('logsDetallados').textContent = logs;
        }

        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testCodigosMixtos, 1000);
        });
    </script>
</body>
</html>

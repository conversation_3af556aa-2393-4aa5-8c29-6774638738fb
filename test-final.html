<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Test Final - Verificación Completa</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .badge { background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Test Final - Verificación Completa</h1>
        <p>Prueba final para verificar que <strong>T. Facturación</strong> aparece correctamente en la aplicación.</p>
        
        <button onclick="testCompleto()">🚀 Test Completo</button>
        <button onclick="mostrarEstructura()">📋 Mostrar Estructura Esperada</button>
    </div>

    <div class="container">
        <h2>📊 Resultados del Test</h2>
        <div id="results" class="result">Haz clic en "Test Completo" para comenzar...</div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🏢 Datos Principales</h2>
            <div id="datosMain" class="result">Esperando test...</div>
        </div>
        
        <div class="container">
            <h2>📦 Artículos con T. Facturación</h2>
            <div id="articulosTable" class="result">Esperando test...</div>
        </div>
    </div>

    <!-- Cargar procesador -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        async function testCompleto() {
            let results = '=== TEST COMPLETO CON T. FACTURACIÓN ===\n\n';
            
            try {
                results += '🔄 Cargando business 6.pdf...\n';
                document.getElementById('results').textContent = results;
                
                const response = await fetch('business 6.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'business 6.pdf', { type: 'application/pdf' });
                
                results += '✅ PDF cargado, procesando...\n';
                document.getElementById('results').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Mostrar datos principales
                const datos = result.extractedData;
                results += '=== DATOS PRINCIPALES ===\n';
                results += `Nombre: "${datos.nombre}"\n`;
                results += `Fecha: "${datos.fecha}"\n`;
                results += `OrdenRepuestos: "${datos.ordenRepuestos}"\n`;
                results += `Cotización: "${datos.cotizacion}"\n`;
                results += `Artículos: ${datos.articulos.length}\n\n`;
                
                // Mostrar artículos con T. Facturación
                if (datos.articulos.length > 0) {
                    results += '=== ARTÍCULOS CON T. FACTURACIÓN ===\n';
                    datos.articulos.forEach((art, i) => {
                        results += `${i+1}. ${art.descripcion}\n`;
                        results += `   Cantidad: ${art.cantidad}\n`;
                        results += `   T. Facturación: "${art.tFacturacion}" ✅\n`;
                        results += `   Precio Unit: $${art.precioUni}\n`;
                        results += `   Total: $${art.total}\n\n`;
                    });
                }
                
                // Mostrar datos principales en panel separado
                mostrarDatosPrincipales(datos);
                
                // Mostrar tabla de artículos
                mostrarTablaArticulos(datos.articulos, datos.nombre, datos.fecha);
                
                results += '✅ VERIFICACIÓN COMPLETA:\n';
                results += `- Nombre extraído: ${datos.nombre ? '✅' : '❌'}\n`;
                results += `- Fecha extraída: ${datos.fecha ? '✅' : '❌'}\n`;
                results += `- OrdenRepuestos: ${datos.ordenRepuestos !== 'Orden no identificada' ? '✅' : '❌'}\n`;
                results += `- Cotización: ${datos.cotizacion !== 'Cotización no identificada' ? '✅' : '❌'}\n`;
                results += `- Artículos con T.Facturación: ${datos.articulos.length > 0 && datos.articulos[0].tFacturacion ? '✅' : '❌'}\n`;
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test completo:', error);
            }
            
            document.getElementById('results').textContent = results;
        }
        
        function mostrarDatosPrincipales(datos) {
            const html = `
NOMBRE: ${datos.nombre}
FECHA: ${datos.fecha}
ORDEN REPUESTOS: ${datos.ordenRepuestos}
COTIZACIÓN: ${datos.cotizacion}
ARTÍCULOS: ${datos.articulos.length}

TOTALES:
- SubTotal: $${datos.totales.subtotal || 'N/A'}
- Descuento: $${datos.totales.descuento || 'N/A'}
- Venta Neta: $${datos.totales.ventaNeta || 'N/A'}
- IVA: $${datos.totales.iva || 'N/A'}
- Total: $${datos.totales.total || 'N/A'}
            `;
            
            document.getElementById('datosMain').textContent = html;
        }
        
        function mostrarTablaArticulos(articulos, cliente, fecha) {
            if (articulos.length === 0) {
                document.getElementById('articulosTable').innerHTML = '<p>No hay artículos para mostrar</p>';
                return;
            }
            
            let html = `
<table>
    <thead>
        <tr>
            <th>Cliente</th>
            <th>Fecha</th>
            <th>Cantidad</th>
            <th>Descripción</th>
            <th>T. Facturación</th>
            <th>Precio Unit.</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
`;
            
            articulos.forEach(art => {
                html += `
        <tr>
            <td>${cliente}</td>
            <td>${fecha}</td>
            <td>${art.cantidad}</td>
            <td>${art.descripcion}</td>
            <td><span class="badge">${art.tFacturacion || 'N/A'}</span></td>
            <td>$${(art.precioUni || 0).toLocaleString('es-ES', {minimumFractionDigits: 2})}</td>
            <td><strong>$${(art.total || 0).toLocaleString('es-ES', {minimumFractionDigits: 2})}</strong></td>
        </tr>
`;
            });
            
            html += `
    </tbody>
</table>
            `;
            
            document.getElementById('articulosTable').innerHTML = html;
        }
        
        function mostrarEstructura() {
            const estructura = `
=== ESTRUCTURA ESPERADA EN LA APLICACIÓN ===

📋 TABLA "Detalle de Artículos" debe mostrar:

┌─────────────────────────────────────┬──────────────┬──────────┬─────────────────────────────┬───────────────┬──────────────┬─────────────┐
│ Cliente                             │ Fecha        │ Cantidad │ Descripción                 │ T. Facturación│ Precio Unit. │ Total       │
├─────────────────────────────────────┼──────────────┼──────────┼─────────────────────────────┼───────────────┼──────────────┼─────────────┤
│ INTERNATIONAL BUSINESS S.A. DE C.V. │ 2025-01-06   │ 1.00     │ FAROL DELANTERO RH HALOGENO │ [Detalle]     │ $22,885.28   │ $14,875.43  │
└─────────────────────────────────────┴──────────────┴──────────┴─────────────────────────────┴───────────────┴──────────────┴─────────────┘

📊 EXPORTACIÓN A EXCEL debe incluir:

Hoja "Artículos":
- ARCHIVO
- NOMBRE  
- FECHA
- CANTIDAD
- ARTÍCULO
- DESCRIPCIÓN
- T.FACTURACIÓN ← ¡IMPORTANTE!
- PROCEDENCIA
- PRECIO UNI
- DESC UNI
- TOTAL

✅ VERIFICACIONES:
1. La columna "T. Facturación" aparece en la tabla web
2. El valor "Detalle" se muestra como badge azul
3. La exportación Excel incluye la columna T.FACTURACIÓN
4. Los datos se mapean correctamente desde 'nombre' a 'cliente'
            `;
            
            document.getElementById('results').textContent = estructura;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testCompleto, 1000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Artículos - Verificación de Patrones</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Artículos - Verificación de Patrones</h1>
        <p>Prueba específica para verificar que el patrón de extracción de artículos funciona con la línea real.</p>
        
        <button onclick="testLineaReal()">🔍 Probar Línea Real</button>
        <button onclick="testPatrones()">📋 Probar Todos los Patrones</button>
    </div>

    <div class="container">
        <h2>📊 Resultados</h2>
        <div id="results" class="result">Haz clic en un botón para comenzar...</div>
    </div>

    <!-- Cargar procesador -->
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        // Línea real extraída del log
        const lineaReal = "2,555.80 1,966.00 Detalle 2.00   FILTRO DE AIRE (C)   688.10";
        
        function testLineaReal() {
            let results = '=== PRUEBA CON LÍNEA REAL ===\n\n';
            results += `Línea a procesar: "${lineaReal}"\n\n`;
            
            try {
                const processor = new ImprovedPDFProcessor();
                const articulo = processor.extractFromAutoExcelTableRow(lineaReal);
                
                if (articulo) {
                    results += '✅ ARTÍCULO EXTRAÍDO EXITOSAMENTE:\n\n';
                    results += `Cantidad: ${articulo.cantidad}\n`;
                    results += `Artículo: ${articulo.articulo}\n`;
                    results += `Descripción: ${articulo.descripcion}\n`;
                    results += `T.Facturación: ${articulo.tFacturacion}\n`;
                    results += `Procedencia: ${articulo.procedencia}\n`;
                    results += `Precio Unitario: $${articulo.precioUni}\n`;
                    results += `Descuento Unitario: $${articulo.descUni}\n`;
                    results += `Total: $${articulo.total}\n`;
                } else {
                    results += '❌ NO SE PUDO EXTRAER EL ARTÍCULO\n';
                    results += 'Revisa la consola para más detalles.\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('results').textContent = results;
        }
        
        function testPatrones() {
            let results = '=== PRUEBA DE TODOS LOS PATRONES ===\n\n';
            
            // Patrones a probar
            const patrones = [
                /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/,
                /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+)$/,
                /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+.+?\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/
            ];
            
            results += `Línea a analizar: "${lineaReal}"\n\n`;
            
            patrones.forEach((patron, index) => {
                results += `--- PATRÓN ${index + 1} ---\n`;
                results += `Expresión: ${patron}\n`;
                
                const match = lineaReal.match(patron);
                if (match) {
                    results += `✅ COINCIDENCIA ENCONTRADA:\n`;
                    match.forEach((grupo, i) => {
                        results += `  Grupo ${i}: "${grupo}"\n`;
                    });
                    
                    // Interpretar los grupos
                    if (match.length >= 6) {
                        results += `\nINTERPRETACIÓN:\n`;
                        results += `  Total: ${match[1]}\n`;
                        results += `  PrecioUni: ${match[2]}\n`;
                        results += `  T.Facturación: ${match[3]}\n`;
                        results += `  Cantidad: ${match[4]}\n`;
                        results += `  Descripción: ${match[5]}\n`;
                        if (match[6]) results += `  DescUni: ${match[6]}\n`;
                    }
                } else {
                    results += `❌ NO HAY COINCIDENCIA\n`;
                }
                results += `\n`;
            });
            
            // Análisis manual de la estructura
            results += '=== ANÁLISIS MANUAL DE LA ESTRUCTURA ===\n\n';
            const partes = lineaReal.split(/\s+/);
            results += 'Partes separadas por espacios:\n';
            partes.forEach((parte, index) => {
                results += `  ${index}: "${parte}"\n`;
            });
            
            results += '\nEstructura esperada:\n';
            results += '  0: Total (2,555.80)\n';
            results += '  1: PrecioUni (1,966.00)\n';
            results += '  2: T.Facturación (Detalle)\n';
            results += '  3: Cantidad (2.00)\n';
            results += '  4-6: Descripción (FILTRO DE AIRE (C))\n';
            results += '  7: DescUni (688.10)\n';
            
            document.getElementById('results').textContent = results;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testLineaReal, 1000);
        });
    </script>
</body>
</html>

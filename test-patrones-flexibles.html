<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Patrones Flexibles</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Patrones Flexibles</h1>
        <p>Prueba de los nuevos patrones flexibles para extraer artículos del PDF <strong>codigos.pdf</strong>.</p>
        
        <div class="success">
            <strong>Mejoras Implementadas:</strong><br>
            ✅ Método `extractArticulosGeneric()` mejorado con logging detallado<br>
            ✅ Nuevo método `extractFromAnyLine()` con patrones flexibles<br>
            ✅ Fallback automático cuando no se encuentra encabezado de tabla<br>
            ✅ Logging completo para diagnóstico
        </div>
        
        <button class="btn btn-primary" onclick="testPatronesFlexibles()">🚀 Probar Patrones Flexibles</button>
        <button class="btn btn-info" onclick="mostrarLogs()">📋 Mostrar Logs Detallados</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Test</h2>
        <div id="resultadoTest" class="result">Haz clic en "Probar Patrones Flexibles" para comenzar...</div>
    </div>

    <div class="container">
        <h2>🔍 Logs Detallados</h2>
        <div id="logsDetallados" class="result">Los logs aparecerán aquí...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    <script src="js/pdf-exporter.js"></script>
    
    <script>
        // Capturar logs de consola
        const originalLog = console.log;
        const originalError = console.error;
        let consoleLogs = [];

        console.log = function(...args) {
            consoleLogs.push(`${args.join(' ')}`);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleLogs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        async function testPatronesFlexibles() {
            let results = '=== TEST PATRONES FLEXIBLES ===\n\n';
            consoleLogs = []; // Limpiar logs
            
            try {
                results += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                if (!response.ok) {
                    throw new Error(`Error cargando PDF: ${response.status}`);
                }
                
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                results += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n`;
                document.getElementById('resultadoTest').textContent = results;
                
                // Procesar con el procesador mejorado
                results += '🔄 Procesando con patrones flexibles...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Análisis de resultados
                results += '=== RESULTADOS ===\n';
                results += `Error: ${result.error || 'Ninguno'}\n`;
                results += `Archivo: ${result.fileName}\n`;
                results += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    results += '=== DATOS EXTRAÍDOS ===\n';
                    results += `Cliente: "${data.nombre || 'N/A'}"\n`;
                    results += `Fecha: "${data.fecha || 'N/A'}"\n`;
                    results += `OrdenRepuestos: "${data.ordenRepuestos || 'N/A'}"\n`;
                    results += `Artículos: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        results += '🎉 ¡ARTÍCULOS EXTRAÍDOS EXITOSAMENTE!\n\n';
                        results += '=== ARTÍCULOS DETALLADOS ===\n';
                        data.articulos.forEach((art, i) => {
                            results += `${i+1}. Cantidad: ${art.cantidad}\n`;
                            results += `   Código: "${art.articulo}"\n`;
                            results += `   Descripción: "${art.descripcion}"\n`;
                            results += `   T.Facturación: ${art.tFacturacion}\n`;
                            results += `   PrecioUni: ${art.precioUni}\n`;
                            results += `   Total: ${art.total}\n\n`;
                        });
                        
                        // Test de detección de códigos
                        results += '=== TEST BOTÓN PDF ===\n';
                        const pdfExporter = new PDFExporter();
                        const hasArticleCodes = pdfExporter.hasArticleCodes([result]);
                        
                        results += `hasArticleCodes: ${hasArticleCodes}\n`;
                        results += `Botón PDF debería aparecer: ${hasArticleCodes ? 'SÍ ✅' : 'NO ❌'}\n\n`;
                        
                        // Contar códigos
                        const codigosEncontrados = data.articulos.filter(art => art.articulo && art.articulo.trim() !== '').length;
                        results += `📊 CÓDIGOS: ${codigosEncontrados}/${data.articulos.length} artículos tienen código\n\n`;
                        
                        results += '=== ESTADO FINAL ===\n';
                        if (data.articulos.length > 0) {
                            results += '✅ ÉXITO: Los artículos se extraen correctamente\n';
                            results += '✅ ÉXITO: La tabla debería mostrar datos\n';
                            if (hasArticleCodes) {
                                results += '✅ ÉXITO: El botón "Exportar PDF Final" debería aparecer\n';
                            } else {
                                results += '⚠️ INFO: No hay códigos, botón PDF no aparecerá\n';
                            }
                        }
                        
                    } else {
                        results += '❌ PROBLEMA: Aún no se encontraron artículos\n';
                        results += '🔍 Revisar logs detallados para diagnóstico\n';
                    }
                } else {
                    results += '❌ NO SE EXTRAJERON DATOS\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoTest').textContent = results;
        }

        function mostrarLogs() {
            let logs = '=== LOGS DETALLADOS DEL PROCESAMIENTO ===\n\n';
            
            if (consoleLogs.length === 0) {
                logs += 'No hay logs disponibles. Ejecuta primero "Probar Patrones Flexibles".\n';
            } else {
                logs += consoleLogs.join('\n');
            }
            
            document.getElementById('logsDetallados').textContent = logs;
        }

        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testPatronesFlexibles, 1000);
        });
    </script>
</body>
</html>

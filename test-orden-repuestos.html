<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Test OrdenRepuestos - Verificación Completa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        table { font-size: 11px; }
        th { background-color: #007bff; color: white; }
        .badge { font-size: 10px; }
        .text-muted { color: #6c757d !important; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Test OrdenRepuestos - Verificación Completa</h1>
        <p>Prueba para verificar que la nueva estructura incluya OrdenRepuestos y manejo inteligente de códigos:</p>
        
        <div class="info">
            <strong>Nueva Estructura:</strong><br>
            Cliente | Vehículo | OrdenRepuestos | Código | Descripción | T. Facturación | Existencia Nacional | Cantidad Solicitada | Fecha Solicitud
        </div>
        
        <button class="btn btn-primary" onclick="testEstructuraCompleta()">🚀 Probar Estructura Completa</button>
        <button class="btn btn-success" onclick="mostrarEjemplos()">📋 Mostrar Ejemplos</button>
    </div>

    <div class="container">
        <h2>📊 Tabla de Prueba</h2>
        <div id="tablaTest" class="table-responsive">Haz clic en "Probar Estructura Completa" para ver la tabla...</div>
    </div>

    <div class="container">
        <h2>📄 Estructura Excel</h2>
        <div id="estructuraExcel" class="result">Esperando test...</div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testEstructuraCompleta() {
            // Datos de ejemplo con diferentes casos de códigos
            const datosEjemplo = [
                {
                    cliente: 'INTERNATIONAL BUSINESS S.A. DE C.V.',
                    vehiculo: '',
                    ordenRepuestos: '250001671',
                    codigo: 'FAROL', // Código válido
                    descripcion: 'FAROL DELANTERO RH HALOGENO',
                    tFacturacion: 'Detalle',
                    existenciaNacional: '',
                    cantidadSolicitada: '1.00',
                    fechaSolicitud: '2025-01-06'
                },
                {
                    cliente: 'ECONO RENT A CAR S. DE R.L',
                    vehiculo: '',
                    ordenRepuestos: '250003367',
                    codigo: '', // Sin código (descripción no empieza con código)
                    descripcion: 'FILTRO DE AIRE (C)',
                    tFacturacion: 'Detalle',
                    existenciaNacional: '',
                    cantidadSolicitada: '2.00',
                    fechaSolicitud: '2025-01-09'
                },
                {
                    cliente: 'EMPRESA EJEMPLO S.A.',
                    vehiculo: '',
                    ordenRepuestos: '250004567',
                    codigo: 'ABC123', // Código con números
                    descripcion: 'ABC123 TORNILLO HEXAGONAL M8',
                    tFacturacion: 'Detalle',
                    existenciaNacional: '',
                    cantidadSolicitada: '5.00',
                    fechaSolicitud: '2025-01-10'
                }
            ];
            
            mostrarTabla(datosEjemplo);
            mostrarEstructuraExcel();
        }
        
        function mostrarTabla(datos) {
            const tableHTML = `
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Cliente</th>
                            <th>Vehículo</th>
                            <th>OrdenRepuestos</th>
                            <th>Código</th>
                            <th>Descripción</th>
                            <th>T. Facturación</th>
                            <th>Existencia Nacional</th>
                            <th>Cantidad Solicitada</th>
                            <th>Fecha Solicitud</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${datos.map(item => `
                            <tr>
                                <td>${item.cliente}</td>
                                <td class="text-muted">-</td>
                                <td><span class="badge bg-info">${item.ordenRepuestos}</span></td>
                                <td>${item.codigo ? item.codigo : '<span class="text-muted">-</span>'}</td>
                                <td class="text-truncate-2">${item.descripcion}</td>
                                <td><span class="badge bg-primary">${item.tFacturacion}</span></td>
                                <td class="text-muted">-</td>
                                <td>${item.cantidadSolicitada}</td>
                                <td>${item.fechaSolicitud}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('tablaTest').innerHTML = tableHTML;
        }
        
        function mostrarEstructuraExcel() {
            const estructura = `
=== ESTRUCTURA EXCEL - HOJA "ARTÍCULOS" ===

Columnas en orden:
1. ARCHIVO
2. CLIENTE
3. VEHÍCULO (en blanco)
4. ORDENREPUESTOS
5. CÓDIGO (en blanco si no hay código válido)
6. DESCRIPCIÓN
7. T.FACTURACIÓN
8. EXISTENCIA NACIONAL (en blanco)
9. CANTIDAD SOLICITADA
10. FECHA SOLICITUD
11. PRECIO UNI
12. DESC UNI
13. TOTAL

Ejemplos de filas:
business 6.pdf | INTERNATIONAL BUSINESS S.A. DE C.V. | | 250001671 | FAROL | FAROL DELANTERO RH HALOGENO | Detalle | | 1.00 | 2025-01-06 | 22885.28 | 8009.85 | 14875.43

3. ECONO.pdf | ECONO RENT A CAR S. DE R.L | | 250003367 | | FILTRO DE AIRE (C) | Detalle | | 2.00 | 2025-01-09 | 1966.00 | 688.10 | 2555.80

✅ CARACTERÍSTICAS:
- OrdenRepuestos aparece como badge azul claro (bg-info)
- Código se deja en blanco si no parece un código válido
- Lógica inteligente: detecta códigos por números, longitud o formato
- Vehículo y Existencia Nacional siguen en blanco
            `;
            
            document.getElementById('estructuraExcel').textContent = estructura;
        }
        
        function mostrarEjemplos() {
            const ejemplos = `
=== EJEMPLOS DE DETECCIÓN DE CÓDIGOS ===

CASOS QUE SÍ EXTRAEN CÓDIGO:
✅ "FAROL DELANTERO RH HALOGENO" → Código: "FAROL"
✅ "ABC123 TORNILLO HEXAGONAL" → Código: "ABC123"
✅ "F-001 FILTRO DE ACEITE" → Código: "F-001"
✅ "12345 PIEZA ESPECIAL" → Código: "12345"

CASOS QUE NO EXTRAEN CÓDIGO (quedan en blanco):
❌ "FILTRO DE AIRE (C)" → Código: (vacío)
❌ "ACEITE MOTOR 15W40" → Código: (vacío)
❌ "PASTILLAS DE FRENO DELANTERAS" → Código: (vacío)

LÓGICA DE DETECCIÓN:
1. Primera palabra contiene números → ES CÓDIGO
2. Primera palabra ≤ 8 caracteres → POSIBLE CÓDIGO
3. Primera palabra solo letras/números/guiones → POSIBLE CÓDIGO
4. Si no cumple criterios → DEJAR EN BLANCO

ORDEN REPUESTOS:
- Se extrae del campo "ordenRepuestos" del PDF
- Se muestra como badge azul claro
- Ejemplos: 250001671, 250003367, 250004567
            `;
            
            document.getElementById('estructuraExcel').textContent = ejemplos;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testEstructuraCompleta, 1000);
        });
    </script>
</body>
</html>

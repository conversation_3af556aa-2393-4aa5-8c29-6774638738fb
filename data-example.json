{"description": "Archivo de ejemplo con datos de prueba para el Dashboard de Cotizaciones", "version": "1.0.0", "sampleData": [{"fileName": "cotizacion-001-empresa-abc.pdf", "fileSize": 245760, "pageCount": 2, "extractedData": {"cliente": "Empresa ABC S.A. de C.V.", "fecha": "2024-01-15", "numeroCotizacion": "COT-2024-001", "articulos": [{"cantidad": 10, "descripcion": "Laptop Dell Inspiron 15 3000", "precio": 12500.0, "total": 125000.0}, {"cantidad": 5, "descripcion": "Monitor Samsung 24 pulgadas", "precio": 3200.0, "total": 16000.0}, {"cantidad": 15, "descripcion": "Teclado inalámbrico Logitech", "precio": 850.0, "total": 12750.0}], "totales": {"subtotal": 153750.0, "iva": 24600.0, "total": 178350.0}}, "processedAt": "2024-01-20T10:30:00.000Z"}, {"fileName": "cotizacion-002-constructora-xyz.pdf", "fileSize": 189440, "pageCount": 1, "extractedData": {"cliente": "Constructora XYZ Ltda.", "fecha": "2024-01-18", "numeroCotizacion": "COT-2024-002", "articulos": [{"cantidad": 100, "descripcion": "Cemento Portland 50kg", "precio": 185.0, "total": 18500.0}, {"cantidad": 50, "descripcion": "<PERSON><PERSON><PERSON> corrugada 3/8", "precio": 95.0, "total": 4750.0}, {"cantidad": 200, "descripcion": "Block hueco 15x20x40", "precio": 12.5, "total": 2500.0}, {"cantidad": 25, "descripcion": "Arena fina m3", "precio": 320.0, "total": 8000.0}], "totales": {"subtotal": 33750.0, "iva": 5400.0, "total": 39150.0}}, "processedAt": "2024-01-20T10:35:00.000Z"}, {"fileName": "cotizacion-003-restaurante-gourmet.pdf", "fileSize": 156672, "pageCount": 1, "extractedData": {"cliente": "Restaurante Gourmet Plaza", "fecha": "2024-01-20", "numeroCotizacion": "COT-2024-003", "articulos": [{"cantidad": 2, "descripcion": "Refrigerador comercial 4 puertas", "precio": 45000.0, "total": 90000.0}, {"cantidad": 1, "descripcion": "Plancha industrial 80cm", "precio": 18500.0, "total": 18500.0}, {"cantidad": 3, "descripcion": "Mesa de trabajo acero inoxidable", "precio": 4200.0, "total": 12600.0}, {"cantidad": 6, "descripcion": "Silla alta para barra", "precio": 1250.0, "total": 7500.0}], "totales": {"subtotal": 128600.0, "iva": 20576.0, "total": 149176.0}}, "processedAt": "2024-01-20T10:40:00.000Z"}, {"fileName": "cotizacion-004-empresa-abc.pdf", "fileSize": 198144, "pageCount": 2, "extractedData": {"cliente": "Empresa ABC S.A. de C.V.", "fecha": "2024-01-22", "numeroCotizacion": "COT-2024-004", "articulos": [{"cantidad": 20, "descripcion": "Impresora multifuncional HP LaserJet", "precio": 8500.0, "total": 170000.0}, {"cantidad": 50, "descripcion": "Cartucho de tóner negro", "precio": 1200.0, "total": 60000.0}, {"cantidad": 25, "descripcion": "Resma papel bond carta", "precio": 85.0, "total": 2125.0}], "totales": {"subtotal": 232125.0, "iva": 37140.0, "total": 269265.0}}, "processedAt": "2024-01-20T10:45:00.000Z"}, {"fileName": "cotizacion-005-clinica-salud.pdf", "fileSize": 167936, "pageCount": 1, "extractedData": {"cliente": "<PERSON><PERSON><PERSON><PERSON> Integral", "fecha": "2024-01-25", "numeroCotizacion": "COT-2024-005", "articulos": [{"cantidad": 1, "descripcion": "Equipo de rayos X digital", "precio": 850000.0, "total": 850000.0}, {"cantidad": 2, "descripcion": "Camilla eléctrica ajustable", "precio": 25000.0, "total": 50000.0}, {"cantidad": 5, "descripcion": "Lámpara de examinación LED", "precio": 3500.0, "total": 17500.0}, {"cantidad": 10, "descripcion": "Silla de espera ergonómica", "precio": 1800.0, "total": 18000.0}], "totales": {"subtotal": 935500.0, "iva": 149680.0, "total": 1085180.0}}, "processedAt": "2024-01-20T10:50:00.000Z"}], "statistics": {"totalFiles": 5, "totalClients": 4, "totalQuotes": 5, "totalItems": 19, "totalAmount": 1721121.0, "averagePerQuote": 344224.2, "clientDistribution": {"Empresa ABC S.A. de C.V.": {"quotes": 2, "amount": 447615.0}, "Constructora XYZ Ltda.": {"quotes": 1, "amount": 39150.0}, "Restaurante Gourmet Plaza": {"quotes": 1, "amount": 149176.0}, "Clínica Salud Integral": {"quotes": 1, "amount": 1085180.0}}}, "testInstructions": {"purpose": "Este archivo contiene datos de ejemplo para probar la funcionalidad del Dashboard de Cotizaciones", "usage": ["1. Los desarrolladores pueden usar estos datos para simular el procesamiento de PDFs", "2. Útil para probar la exportación a Excel sin necesidad de archivos PDF reales", "3. Permite validar los cálculos y la lógica de agrupación", "4. <PERSON>ve como referencia para el formato esperado de datos extraídos"], "notes": ["Los datos representan cotizaciones típicas de diferentes industrias", "Incluye variedad en tipos de productos y rangos de precios", "Simula tanto clientes únicos como recurrentes", "Los totales están calculados correctamente para validar la lógica"]}}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Patrones - An<PERSON><PERSON>is de Estructura</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 10px;
            border: 1px solid #dee2e6;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Patrones - Análisis de Estructura</h1>
        <p>Análisis detallado de la estructura del PDF para crear patrones de extracción correctos.</p>
        
        <div class="warning">
            <strong>Objetivo:</strong> Identificar la estructura exacta del PDF para ajustar los patrones regex y extraer artículos correctamente.
        </div>
        
        <button class="btn btn-primary" onclick="analizarEstructura()">🔍 Analizar Estructura</button>
        <button class="btn btn-secondary" onclick="probarPatrones()">🧪 Probar Patrones</button>
        <button class="btn btn-info" onclick="crearPatronPersonalizado()">⚙️ Crear Patrón</button>
    </div>

    <div class="container">
        <h2>📄 Texto Completo del PDF</h2>
        <div id="textoCompleto" class="result">Esperando análisis...</div>
    </div>

    <div class="container">
        <h2>📊 Análisis de Líneas</h2>
        <div id="analisisLineas" class="result">Esperando análisis...</div>
    </div>

    <div class="container">
        <h2>🧪 Test de Patrones</h2>
        <div id="testPatrones" class="result">Esperando test...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    
    <script>
        let textoCompletoPDF = '';
        let lineasPDF = [];

        async function analizarEstructura() {
            try {
                // Cargar PDF
                const response = await fetch('codigos.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                // Extraer texto
                textoCompletoPDF = await extraerTextoCompleto(file);
                lineasPDF = textoCompletoPDF.split('\n').map(l => l.trim()).filter(l => l.length > 0);
                
                // Mostrar texto completo
                document.getElementById('textoCompleto').textContent = textoCompletoPDF;
                
                // Analizar líneas
                analizarLineasDetallado();
                
            } catch (error) {
                document.getElementById('textoCompleto').textContent = `Error: ${error.message}`;
            }
        }

        function analizarLineasDetallado() {
            let analisis = '=== ANÁLISIS DETALLADO DE LÍNEAS ===\n\n';
            
            analisis += `Total de líneas: ${lineasPDF.length}\n\n`;
            
            // Analizar cada línea
            lineasPDF.forEach((linea, i) => {
                if (linea.length > 10) { // Solo líneas significativas
                    analisis += `Línea ${i+1}: "${linea}"\n`;
                    
                    // Detectar números
                    const numeros = linea.match(/\d+[.,]?\d*/g);
                    if (numeros && numeros.length > 0) {
                        analisis += `  Números: ${numeros.join(', ')}\n`;
                    }
                    
                    // Detectar precios (formato con decimales)
                    const precios = linea.match(/\d+[.,]\d{2}/g);
                    if (precios && precios.length > 0) {
                        analisis += `  Precios: ${precios.join(', ')}\n`;
                    }
                    
                    // Detectar palabras clave
                    const palabrasClave = ['Cant', 'Articulo', 'Descripcion', 'Precio', 'Total', 'Detalle'];
                    const encontradas = palabrasClave.filter(p => linea.toLowerCase().includes(p.toLowerCase()));
                    if (encontradas.length > 0) {
                        analisis += `  Palabras clave: ${encontradas.join(', ')}\n`;
                    }
                    
                    // Detectar si parece fila de tabla (múltiples números + texto)
                    if (numeros && numeros.length >= 2 && linea.length > 20) {
                        analisis += `  ⭐ POSIBLE FILA DE TABLA\n`;
                    }
                    
                    analisis += '\n';
                }
            });
            
            document.getElementById('analisisLineas').textContent = analisis;
        }

        function probarPatrones() {
            if (lineasPDF.length === 0) {
                document.getElementById('testPatrones').textContent = 'Primero ejecuta "Analizar Estructura"';
                return;
            }
            
            let test = '=== TEST DE PATRONES EXISTENTES ===\n\n';
            
            // Patrones actuales del procesador
            const patrones = [
                {
                    nombre: 'Patrón Principal',
                    regex: /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/,
                    descripcion: 'Total PrecioUni T.Facturacion Cantidad Descripción DescUni'
                },
                {
                    nombre: 'Patrón Alternativo',
                    regex: /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+(\w+)\s+(\d+\.?\d*)\s+(.+)$/,
                    descripcion: 'Total PrecioUni T.Facturacion Cantidad Descripción'
                },
                {
                    nombre: 'Patrón Simple',
                    regex: /^([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+.+?\s+(\d+\.?\d*)\s+(.+?)\s+([\d,]+\.?\d*)$/,
                    descripcion: 'Números + texto'
                }
            ];
            
            // Probar cada patrón con cada línea
            patrones.forEach((patron, p) => {
                test += `=== ${patron.nombre} ===\n`;
                test += `Regex: ${patron.regex}\n`;
                test += `Descripción: ${patron.descripcion}\n\n`;
                
                let coincidencias = 0;
                
                lineasPDF.forEach((linea, i) => {
                    const match = linea.match(patron.regex);
                    if (match) {
                        coincidencias++;
                        test += `✅ Línea ${i+1}: "${linea}"\n`;
                        test += `   Grupos: ${match.slice(1).join(' | ')}\n\n`;
                    }
                });
                
                test += `Total coincidencias: ${coincidencias}\n\n`;
            });
            
            // Crear patrones personalizados basados en el contenido
            test += '=== PATRONES SUGERIDOS ===\n\n';
            
            // Buscar líneas que parezcan filas de tabla
            const posiblesFilas = lineasPDF.filter(linea => {
                const numeros = linea.match(/\d+/g);
                return numeros && numeros.length >= 3 && linea.length > 20;
            });
            
            if (posiblesFilas.length > 0) {
                test += 'Posibles filas de tabla encontradas:\n';
                posiblesFilas.forEach((fila, i) => {
                    test += `${i+1}. "${fila}"\n`;
                });
                
                // Analizar estructura común
                test += '\nAnálisis de estructura:\n';
                const primeraFila = posiblesFilas[0];
                const partes = primeraFila.split(/\s+/);
                test += `Partes separadas por espacios: ${partes.length}\n`;
                test += `Partes: ${partes.join(' | ')}\n`;
            }
            
            document.getElementById('testPatrones').textContent = test;
        }

        function crearPatronPersonalizado() {
            if (lineasPDF.length === 0) {
                document.getElementById('testPatrones').textContent = 'Primero ejecuta "Analizar Estructura"';
                return;
            }
            
            let patron = '=== CREACIÓN DE PATRÓN PERSONALIZADO ===\n\n';
            
            // Buscar la línea de encabezado
            const encabezado = lineasPDF.find(linea => 
                linea.toLowerCase().includes('cant') || 
                linea.toLowerCase().includes('descripcion') ||
                linea.toLowerCase().includes('articulo')
            );
            
            if (encabezado) {
                patron += `Encabezado encontrado: "${encabezado}"\n\n`;
                
                // Analizar estructura del encabezado
                const columnas = encabezado.split(/\s+/);
                patron += `Columnas detectadas: ${columnas.join(' | ')}\n\n`;
            }
            
            // Buscar líneas de datos después del encabezado
            const indiceDatos = lineasPDF.findIndex(linea => 
                linea.toLowerCase().includes('cant') || 
                linea.toLowerCase().includes('descripcion')
            );
            
            if (indiceDatos >= 0) {
                patron += 'Líneas de datos después del encabezado:\n';
                
                for (let i = indiceDatos + 1; i < Math.min(indiceDatos + 10, lineasPDF.length); i++) {
                    const linea = lineasPDF[i];
                    if (linea.length > 10) {
                        patron += `${i+1}. "${linea}"\n`;
                        
                        // Intentar identificar estructura
                        const numeros = linea.match(/\d+[.,]?\d*/g);
                        const palabras = linea.split(/\s+/);
                        
                        if (numeros && numeros.length >= 2) {
                            patron += `   Números: ${numeros.join(' | ')}\n`;
                            patron += `   Palabras: ${palabras.length}\n`;
                            
                            // Sugerir patrón
                            if (numeros.length >= 3) {
                                patron += `   ⭐ POSIBLE ESTRUCTURA: Cantidad | Descripción | Precios\n`;
                            }
                        }
                        patron += '\n';
                    }
                }
            }
            
            document.getElementById('testPatrones').textContent = patron;
        }

        async function extraerTextoCompleto(file) {
            const arrayBuffer = await file.arrayBuffer();
            const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
            
            let textoCompleto = '';
            
            for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();
                
                textoCompleto += `\n=== PÁGINA ${pageNum} ===\n`;
                textContent.items.forEach(item => {
                    textoCompleto += item.str + '\n';
                });
            }
            
            return textoCompleto;
        }

        // Ejecutar análisis automáticamente
        window.addEventListener('load', () => {
            setTimeout(analizarEstructura, 1000);
        });
    </script>
</body>
</html>

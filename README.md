# 📊 Dashboard de Cotizaciones - Análisis Inteligente de PDFs

## 🎯 Descripción del Proyecto

Aplicación web inteligente, totalmente autoejecutable y sin necesidad de instalación, que permite al equipo de trabajo:

- ✅ Cargar y analizar cotizaciones en PDF desde una carpeta local
- 🧠 Extraer automáticamente información crítica (cliente, fecha, artículos, precios)
- 📊 Agrupar y organizar las cotizaciones por cliente o fecha
- 📤 Generar un reporte final en Excel con toda la información consolidada

## 🚀 Características Principales

- **100% Local**: No requiere instalación ni conexión a internet
- **Sin servidor**: Se ejecuta directamente desde el archivo `index.html`
- **Lectura automática de PDFs**: Drag & drop o selector de archivos
- **Extracción inteligente**: Algoritmos avanzados de procesamiento de texto
- **Dashboard visual**: Interfaz moderna y responsiva
- **Exportación a Excel**: Reportes detallados con múltiples hojas
- **Privacidad total**: Ningún dato sale del equipo

## 📁 Estructura del Proyecto

```
cotizaciones-dashboard/
├── index.html              # Aplicación principal (abrir en navegador)
├── README.md               # Este archivo
├── css/
│   └── styles.css          # Estilos personalizados
├── js/
│   ├── app.js              # Controlador principal
│   ├── pdf-processor.js    # Procesamiento de PDFs
│   └── excel-exporter.js   # Exportación a Excel
└── libs/                   # Librerías externas (se cargan desde CDN)
```

## 🛠️ Instalación y Uso

### Requisitos Mínimos
- Navegador web moderno (Chrome, Firefox, Edge, Safari)
- JavaScript habilitado
- Conexión a internet (solo para cargar librerías CDN)

### Instrucciones de Uso

1. **Abrir la aplicación**:
   - Hacer doble clic en `index.html`
   - O abrir con el navegador de preferencia

2. **Cargar archivos PDF**:
   - Arrastrar y soltar archivos PDF en la zona designada
   - O hacer clic para seleccionar archivos

3. **Procesar documentos**:
   - Hacer clic en "Procesar PDFs"
   - Esperar a que termine el análisis automático

4. **Revisar resultados**:
   - Ver estadísticas en tiempo real
   - Revisar tablas de cotizaciones y artículos
   - Usar filtros para buscar información específica

5. **Exportar reporte**:
   - Hacer clic en "Exportar a Excel"
   - El archivo se descargará automáticamente

## 📊 Información Extraída

La aplicación extrae automáticamente:

### Datos de Cotización
- **Cliente**: Nombre de la empresa o persona
- **Fecha**: Fecha de la cotización
- **Número**: Folio o número de cotización
- **Estado**: Estado del procesamiento

### Datos de Artículos
- **Cantidad**: Número de unidades
- **Descripción**: Nombre del producto/servicio
- **Precio Unitario**: Precio por unidad
- **Total**: Cantidad × Precio unitario

### Totales
- **Subtotal**: Suma antes de impuestos
- **IVA**: Impuestos aplicados
- **Total**: Monto final

## 📈 Reportes Generados

El archivo Excel incluye las siguientes hojas:

1. **Resumen**: Estadísticas generales y distribución por cliente
2. **Cotizaciones**: Lista completa de todas las cotizaciones
3. **Artículos**: Detalle de todos los artículos cotizados
4. **Clientes**: Análisis por cliente con totales y promedios
5. **Estadísticas**: Distribución mensual y artículos más cotizados

## 🔧 Configuración Avanzada

### Patrones de Extracción

La aplicación utiliza expresiones regulares para identificar:

- **Clientes**: Busca patrones como "Cliente:", "Para:", "Empresa:"
- **Fechas**: Reconoce formatos DD/MM/YYYY, DD-MM-YYYY, etc.
- **Precios**: Identifica montos con $ y formatos numéricos
- **Artículos**: Detecta líneas con cantidad + descripción + precio

### Personalización

Para modificar los patrones de extracción, editar el archivo `js/pdf-processor.js`:

```javascript
this.patterns = {
    cliente: [
        /(?:cliente|client|empresa)[\s:]+([^\n\r]+)/i,
        // Agregar más patrones aquí
    ],
    // ... otros patrones
};
```

## 🛡️ Seguridad y Privacidad

- **Procesamiento local**: Todos los datos se procesan en el navegador
- **Sin envío de datos**: Ninguna información se envía a servidores externos
- **Sin almacenamiento**: Los datos no se guardan permanentemente
- **Compatible con políticas IT**: Funciona en entornos corporativos restrictivos

## 🐛 Solución de Problemas

### Problemas Comunes

**Error: "No se puede procesar el PDF"**
- Verificar que el archivo no esté protegido con contraseña
- Asegurar que el PDF contiene texto (no solo imágenes)

**Error: "No se detectan artículos"**
- El PDF puede tener un formato no estándar
- Revisar manualmente y ajustar patrones de extracción

**Error: "No se puede exportar a Excel"**
- Verificar que el navegador permite descargas
- Intentar con un navegador diferente

### Limitaciones Conocidas

- PDFs escaneados (solo imágenes) requieren OCR externo
- Formatos de cotización muy personalizados pueden necesitar ajustes
- Archivos muy grandes (>50MB) pueden ser lentos de procesar

## 📞 Soporte Técnico

Para reportar problemas o solicitar mejoras:

1. Verificar la consola del navegador (F12) para errores
2. Documentar el tipo de PDF que causa problemas
3. Proporcionar ejemplos de datos no detectados correctamente

## 🔄 Actualizaciones

### Versión 1.0.0 (Actual)
- Extracción básica de datos de cotizaciones
- Exportación a Excel con múltiples hojas
- Interfaz responsive y moderna
- Procesamiento por lotes

### Próximas Mejoras
- Soporte para más formatos de PDF
- Detección mejorada de artículos
- Gráficos y visualizaciones
- Plantillas de exportación personalizables

## 📄 Licencia

Este proyecto es de uso interno. Todos los derechos reservados.

---

**Desarrollado con ❤️ para optimizar el análisis de cotizaciones**

/**
 * PDF Processor Module
 * Handles PDF text extraction and intelligent data parsing
 */

class PDFProcessor {
    constructor() {
        this.pdfjsLib = window['pdfjs-dist/build/pdf'];
        this.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        
        // Patterns for data extraction
        this.patterns = {
            // Cliente patterns
            cliente: [
                /(?:cliente|client|empresa|company)[\s:]+([^\n\r]+)/i,
                /(?:para|to|destinatario)[\s:]+([^\n\r]+)/i,
                /(?:señor|sr|sra|señora)[\s\.]+([^\n\r]+)/i
            ],
            
            // Fecha patterns
            fecha: [
                /(?:fecha|date)[\s:]+(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i,
                /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/g,
                /(?:cotización|quote)[\s\w]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/i
            ],
            
            // Número de cotización
            numeroCotizacion: [
                /(?:cotización|quote|cot|no|número|#)[\s\.:]*([A-Z0-9\-]+)/i,
                /(?:folio|ref|referencia)[\s\.:]*([A-Z0-9\-]+)/i
            ],
            
            // Artículos/Items patterns
            articulos: [
                // Patrón para líneas de productos con cantidad, descripción y precio
                /(\d+(?:\.\d+)?)\s+([^\d\$]+?)\s+\$?([\d,]+(?:\.\d{2})?)/g,
                // Patrón alternativo
                /^(\d+)\s+(.+?)\s+(\$?[\d,]+(?:\.\d{2})?)$/gm
            ],
            
            // Precios
            precios: [
                /\$?([\d,]+(?:\.\d{2})?)/g,
                /(?:total|subtotal|precio)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i
            ]
        };
    }

    /**
     * Process a single PDF file
     * @param {File} file - PDF file to process
     * @returns {Promise<Object>} Extracted data
     */
    async processPDF(file) {
        try {
            const arrayBuffer = await this.fileToArrayBuffer(file);
            const pdf = await this.pdfjsLib.getDocument(arrayBuffer).promise;
            
            let fullText = '';
            const pageTexts = [];
            
            // Extract text from all pages
            for (let i = 1; i <= pdf.numPages; i++) {
                const page = await pdf.getPage(i);
                const textContent = await page.getTextContent();
                const pageText = textContent.items.map(item => item.str).join(' ');
                pageTexts.push(pageText);
                fullText += pageText + '\n';
            }
            
            // Parse extracted data
            const extractedData = this.parseExtractedText(fullText, file.name);
            
            return {
                fileName: file.name,
                fileSize: file.size,
                pageCount: pdf.numPages,
                rawText: fullText,
                pageTexts: pageTexts,
                extractedData: extractedData,
                processedAt: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('Error processing PDF:', error);
            throw new Error(`Error procesando ${file.name}: ${error.message}`);
        }
    }

    /**
     * Convert File to ArrayBuffer
     * @param {File} file 
     * @returns {Promise<ArrayBuffer>}
     */
    fileToArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Parse extracted text to find structured data
     * @param {string} text - Raw text from PDF
     * @param {string} fileName - Original file name
     * @returns {Object} Parsed data
     */
    parseExtractedText(text, fileName) {
        const data = {
            cliente: this.extractCliente(text),
            fecha: this.extractFecha(text),
            numeroCotizacion: this.extractNumeroCotizacion(text),
            articulos: this.extractArticulos(text),
            totales: this.extractTotales(text),
            fileName: fileName
        };

        // Clean and validate data
        return this.cleanExtractedData(data);
    }

    /**
     * Extract client information
     * @param {string} text 
     * @returns {string}
     */
    extractCliente(text) {
        for (const pattern of this.patterns.cliente) {
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim().replace(/[^\w\s\.\-]/g, '');
            }
        }
        return 'Cliente no identificado';
    }

    /**
     * Extract date information
     * @param {string} text 
     * @returns {string}
     */
    extractFecha(text) {
        for (const pattern of this.patterns.fecha) {
            const match = text.match(pattern);
            if (match && match[1]) {
                return this.normalizeDate(match[1]);
            }
        }
        return new Date().toISOString().split('T')[0]; // Default to today
    }

    /**
     * Extract quote number
     * @param {string} text 
     * @returns {string}
     */
    extractNumeroCotizacion(text) {
        for (const pattern of this.patterns.numeroCotizacion) {
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return 'N/A';
    }

    /**
     * Extract articles/items from text
     * @param {string} text 
     * @returns {Array}
     */
    extractArticulos(text) {
        const articulos = [];
        const lines = text.split('\n');
        
        // Try different extraction strategies
        for (const line of lines) {
            const cleanLine = line.trim();
            if (cleanLine.length < 10) continue; // Skip short lines
            
            // Strategy 1: Quantity + Description + Price pattern
            const match1 = cleanLine.match(/^(\d+(?:\.\d+)?)\s+(.+?)\s+\$?([\d,]+(?:\.\d{2})?)$/);
            if (match1) {
                articulos.push({
                    cantidad: parseFloat(match1[1]),
                    descripcion: match1[2].trim(),
                    precio: this.parsePrice(match1[3]),
                    total: parseFloat(match1[1]) * this.parsePrice(match1[3])
                });
                continue;
            }
            
            // Strategy 2: Look for price patterns in lines with descriptions
            const priceMatch = cleanLine.match(/\$?([\d,]+(?:\.\d{2})?)$/);
            if (priceMatch && cleanLine.length > 20) {
                const precio = this.parsePrice(priceMatch[1]);
                const descripcion = cleanLine.replace(/\$?[\d,]+(?:\.\d{2})?$/, '').trim();
                
                if (descripcion.length > 5) {
                    articulos.push({
                        cantidad: 1,
                        descripcion: descripcion,
                        precio: precio,
                        total: precio
                    });
                }
            }
        }
        
        return articulos.length > 0 ? articulos : this.extractArticulosAlternative(text);
    }

    /**
     * Alternative article extraction method
     * @param {string} text 
     * @returns {Array}
     */
    extractArticulosAlternative(text) {
        const articulos = [];
        const words = text.split(/\s+/);
        
        // Look for product-like patterns
        for (let i = 0; i < words.length - 2; i++) {
            const word = words[i];
            
            // If we find a price pattern
            if (word.match(/^\$?[\d,]+(?:\.\d{2})?$/)) {
                const precio = this.parsePrice(word);
                
                // Look backwards for description
                let descripcion = '';
                for (let j = Math.max(0, i - 10); j < i; j++) {
                    if (!words[j].match(/^\d+$/) && !words[j].match(/^\$?[\d,]+(?:\.\d{2})?$/)) {
                        descripcion += words[j] + ' ';
                    }
                }
                
                if (descripcion.trim().length > 5) {
                    articulos.push({
                        cantidad: 1,
                        descripcion: descripcion.trim(),
                        precio: precio,
                        total: precio
                    });
                }
            }
        }
        
        return articulos;
    }

    /**
     * Extract totals from text
     * @param {string} text 
     * @returns {Object}
     */
    extractTotales(text) {
        const totales = {};
        
        // Look for total patterns
        const totalMatch = text.match(/(?:total|grand\s+total)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i);
        if (totalMatch) {
            totales.total = this.parsePrice(totalMatch[1]);
        }
        
        const subtotalMatch = text.match(/(?:subtotal|sub\s+total)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i);
        if (subtotalMatch) {
            totales.subtotal = this.parsePrice(subtotalMatch[1]);
        }
        
        const ivaMatch = text.match(/(?:iva|tax|impuesto)[\s:]*\$?([\d,]+(?:\.\d{2})?)/i);
        if (ivaMatch) {
            totales.iva = this.parsePrice(ivaMatch[1]);
        }
        
        return totales;
    }

    /**
     * Parse price string to number
     * @param {string} priceStr 
     * @returns {number}
     */
    parsePrice(priceStr) {
        if (!priceStr) return 0;
        return parseFloat(priceStr.replace(/[$,]/g, '')) || 0;
    }

    /**
     * Normalize date format
     * @param {string} dateStr 
     * @returns {string}
     */
    normalizeDate(dateStr) {
        try {
            const date = new Date(dateStr.replace(/[\/\-\.]/g, '/'));
            return date.toISOString().split('T')[0];
        } catch {
            return new Date().toISOString().split('T')[0];
        }
    }

    /**
     * Clean and validate extracted data
     * @param {Object} data 
     * @returns {Object}
     */
    cleanExtractedData(data) {
        // Clean cliente name
        if (data.cliente) {
            data.cliente = data.cliente.replace(/[^\w\s\.\-]/g, '').trim();
            if (data.cliente.length > 100) {
                data.cliente = data.cliente.substring(0, 100) + '...';
            }
        }
        
        // Validate and clean articles
        data.articulos = data.articulos.filter(item => 
            item.descripcion && 
            item.descripcion.length > 2 && 
            item.precio > 0
        );
        
        // Calculate totals if not found
        if (data.articulos.length > 0 && !data.totales.total) {
            data.totales.total = data.articulos.reduce((sum, item) => sum + (item.total || 0), 0);
        }
        
        return data;
    }

    /**
     * Process multiple PDF files
     * @param {FileList} files 
     * @param {Function} progressCallback 
     * @returns {Promise<Array>}
     */
    async processMultiplePDFs(files, progressCallback) {
        const results = [];
        const total = files.length;
        
        for (let i = 0; i < total; i++) {
            try {
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: files[i].name,
                        status: 'processing'
                    });
                }
                
                const result = await this.processPDF(files[i]);
                results.push(result);
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: files[i].name,
                        status: 'completed'
                    });
                }
                
            } catch (error) {
                console.error(`Error processing ${files[i].name}:`, error);
                results.push({
                    fileName: files[i].name,
                    error: error.message,
                    processedAt: new Date().toISOString()
                });
                
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: total,
                        fileName: files[i].name,
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }
        
        return results;
    }
}

// Export for use in other modules
window.PDFProcessor = PDFProcessor;

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Test Código en Blanco - Verificación</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
        table { font-size: 11px; }
        th { background-color: #007bff; color: white; }
        .badge { font-size: 10px; }
        .text-muted { color: #6c757d !important; }
        .codigo-vacio { background-color: #f8f9fa; color: #6c757d; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Test Código en Blanco - Verificación</h1>
        <p>Prueba para verificar que el campo <strong>Código</strong> quede en blanco cuando no hay datos específicos en la columna "Artículo" del PDF.</p>
        
        <div class="info">
            <strong>Regla:</strong> El campo Código solo debe mostrar datos que estén específicamente en la columna "Artículo" del PDF. Si no hay esa columna o está vacía, debe quedar en blanco.
        </div>
        
        <button class="btn btn-primary" onclick="testCodigoBlanco()">🚀 Probar PDF Real</button>
        <button class="btn btn-success" onclick="mostrarEjemplo()">📋 Mostrar Ejemplo</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Test</h2>
        <div id="resultadoTest" class="result">Haz clic en "Probar PDF Real" para comenzar...</div>
    </div>

    <div class="container">
        <h2>📋 Tabla Esperada</h2>
        <div id="tablaEsperada" class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Cliente</th>
                        <th>Vehículo</th>
                        <th>OrdenRepuestos</th>
                        <th>Código</th>
                        <th>Descripción</th>
                        <th>T. Facturación</th>
                        <th>Existencia Nacional</th>
                        <th>Cantidad Solicitada</th>
                        <th>Fecha Solicitud</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>BLINDAJES Y TECNOLOGIAS ISRAELITAS S.A</td>
                        <td class="text-muted">-</td>
                        <td><span class="badge bg-info">250004360</span></td>
                        <td class="codigo-vacio">-</td>
                        <td>FILTRO DE ACEITE</td>
                        <td><span class="badge bg-primary">Detalle</span></td>
                        <td class="text-muted">-</td>
                        <td>3</td>
                        <td>2025-01-11</td>
                    </tr>
                    <tr>
                        <td>INTERNATIONAL BUSINESS S.A. DE C.V.</td>
                        <td class="text-muted">-</td>
                        <td><span class="badge bg-info">250001671</span></td>
                        <td class="codigo-vacio">-</td>
                        <td>FAROL DELANTERO RH HALOGENO</td>
                        <td><span class="badge bg-primary">Detalle</span></td>
                        <td class="text-muted">-</td>
                        <td>1.00</td>
                        <td>2025-01-06</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Cargar procesador -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        async function testCodigoBlanco() {
            let results = '=== TEST CÓDIGO EN BLANCO ===\n\n';
            
            try {
                results += '🔄 Cargando PDF de prueba...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                // Probar con business 6.pdf
                const response = await fetch('business 6.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'business 6.pdf', { type: 'application/pdf' });
                
                results += '✅ PDF cargado, procesando...\n';
                document.getElementById('resultadoTest').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Verificar artículos
                const datos = result.extractedData;
                results += '=== VERIFICACIÓN DE CÓDIGOS ===\n';
                results += `Cliente: ${datos.nombre}\n`;
                results += `OrdenRepuestos: ${datos.ordenRepuestos}\n`;
                results += `Artículos encontrados: ${datos.articulos.length}\n\n`;
                
                if (datos.articulos.length > 0) {
                    datos.articulos.forEach((art, i) => {
                        results += `${i+1}. Descripción: "${art.descripcion}"\n`;
                        results += `   Código: "${art.articulo}" ${art.articulo === '' ? '✅ (CORRECTO - EN BLANCO)' : '❌ (INCORRECTO - DEBERÍA ESTAR EN BLANCO)'}\n`;
                        results += `   Cantidad: ${art.cantidad}\n`;
                        results += `   T.Facturación: ${art.tFacturacion}\n\n`;
                    });
                }
                
                // Verificación final
                const todosCodigos = datos.articulos.map(art => art.articulo);
                const todosVacios = todosCodigos.every(codigo => codigo === '' || codigo === null || codigo === undefined);
                
                results += '=== RESULTADO FINAL ===\n';
                results += `Todos los códigos están en blanco: ${todosVacios ? '✅ SÍ' : '❌ NO'}\n`;
                
                if (todosVacios) {
                    results += '🎉 ¡PERFECTO! El campo Código está correctamente en blanco.\n';
                } else {
                    results += '⚠️ PROBLEMA: Algunos códigos no están en blanco cuando deberían estarlo.\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoTest').textContent = results;
        }
        
        function mostrarEjemplo() {
            const ejemplo = `
=== EJEMPLO DE COMPORTAMIENTO CORRECTO ===

ANTES (INCORRECTO):
Cliente: BLINDAJES Y TECNOLOGIAS ISRAELITAS S.A
Código: "FILTRO" ❌ (extraído de la descripción)
Descripción: "FILTRO DE ACEITE"

DESPUÉS (CORRECTO):
Cliente: BLINDAJES Y TECNOLOGIAS ISRAELITAS S.A  
Código: "" ✅ (en blanco porque no hay columna "Artículo" en el PDF)
Descripción: "FILTRO DE ACEITE"

REGLA:
- Solo mostrar en "Código" los datos que estén específicamente en una columna llamada "Artículo" en el PDF
- Si no existe esa columna o está vacía, dejar el campo "Código" en blanco
- NO extraer códigos de la descripción ni inventar códigos

FORMATO ACTUAL DEL PDF:
El PDF tiene columnas: Cant | Descripción | T.Facturación | PrecioUni | Total
NO tiene columna "Artículo" separada
Por lo tanto: Código debe estar SIEMPRE en blanco
            `;
            
            document.getElementById('resultadoTest').textContent = ejemplo;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(testCodigoBlanco, 1000);
        });
    </script>
</body>
</html>

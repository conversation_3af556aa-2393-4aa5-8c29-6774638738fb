<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test FORMATO.pdf - <PERSON>ces<PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .processor-result {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }
        .original { border-left: 3px solid #dc3545; }
        .improved { border-left: 3px solid #28a745; }
        .text-output {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; }
        .data-item {
            background: #e7f3ff;
            padding: 8px;
            margin: 4px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test FORMATO.pdf - Comparación de Procesadores</h1>
        <p>Esta página compara el procesador original vs el mejorado usando el archivo FORMATO.pdf</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testBothProcessors()">🚀 Probar Ambos Procesadores</button>
            <button onclick="testOnlyImproved()">⚡ Solo Procesador Mejorado</button>
            <button onclick="showRawText()">📄 Ver Texto Extraído</button>
        </div>
    </div>

    <div id="loadingContainer" class="container" style="display: none;">
        <div class="loading">
            <div class="spinner"></div>
            <p>Procesando FORMATO.pdf...</p>
            <p id="loadingStatus">Iniciando...</p>
        </div>
    </div>

    <div id="resultsContainer" style="display: none;">
        <div class="container">
            <h2>📊 Resultados de Procesamiento</h2>
            <div class="comparison">
                <div class="processor-result original">
                    <h3>🔴 Procesador Original</h3>
                    <div id="originalResults">No ejecutado</div>
                </div>
                <div class="processor-result improved">
                    <h3>🟢 Procesador Mejorado</h3>
                    <div id="improvedResults">No ejecutado</div>
                </div>
            </div>
        </div>

        <div class="container">
            <h2>📋 Datos Extraídos - Comparación</h2>
            <div class="comparison">
                <div>
                    <h4>Original</h4>
                    <div id="originalData" class="text-output">No hay datos</div>
                </div>
                <div>
                    <h4>Mejorado</h4>
                    <div id="improvedData" class="text-output">No hay datos</div>
                </div>
            </div>
        </div>

        <div class="container">
            <h2>📄 Texto Extraído del PDF</h2>
            <div id="rawTextContainer" class="text-output" style="max-height: 400px;">
                Haz clic en "Ver Texto Extraído" para mostrar el contenido...
            </div>
        </div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    
    <!-- Cargar procesadores -->
    <script src="js/pdf-processor.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    
    <script>
        let rawTextData = '';
        
        function showLoading(show, status = 'Procesando...') {
            document.getElementById('loadingContainer').style.display = show ? 'block' : 'none';
            document.getElementById('resultsContainer').style.display = show ? 'none' : 'block';
            if (show) {
                document.getElementById('loadingStatus').textContent = status;
            }
        }
        
        async function testBothProcessors() {
            showLoading(true, 'Iniciando comparación de procesadores...');
            
            try {
                // Cargar archivo FORMATO.pdf
                showLoading(true, 'Cargando FORMATO.pdf...');
                const response = await fetch('FORMATO.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'FORMATO.pdf', { type: 'application/pdf' });
                
                // Test procesador original
                showLoading(true, 'Probando procesador original...');
                let originalResult = null;
                let originalError = null;
                
                try {
                    const originalProcessor = new PDFProcessor();
                    originalResult = await originalProcessor.processPDF(file);
                    document.getElementById('originalResults').innerHTML = `
                        <div class="success">✅ Procesamiento exitoso</div>
                        <p><strong>Tiempo:</strong> ${originalResult.processingTime || 'N/A'}ms</p>
                        <p><strong>Páginas:</strong> ${originalResult.pageCount}</p>
                        <p><strong>Caracteres:</strong> ${originalResult.rawText?.length || 0}</p>
                    `;
                } catch (error) {
                    originalError = error;
                    document.getElementById('originalResults').innerHTML = `
                        <div class="error">❌ Error: ${error.message}</div>
                    `;
                }
                
                // Test procesador mejorado
                showLoading(true, 'Probando procesador mejorado...');
                let improvedResult = null;
                let improvedError = null;
                
                try {
                    const improvedProcessor = new ImprovedPDFProcessor();
                    improvedResult = await improvedProcessor.processPDF(file);
                    document.getElementById('improvedResults').innerHTML = `
                        <div class="success">✅ Procesamiento exitoso</div>
                        <p><strong>Tiempo:</strong> ${improvedResult.processingTime}ms</p>
                        <p><strong>Páginas:</strong> ${improvedResult.pageCount}</p>
                        <p><strong>Caracteres:</strong> ${improvedResult.rawText.length}</p>
                    `;
                    
                    // Guardar texto para mostrar después
                    rawTextData = improvedResult.rawText;
                    
                } catch (error) {
                    improvedError = error;
                    document.getElementById('improvedResults').innerHTML = `
                        <div class="error">❌ Error: ${error.message}</div>
                    `;
                }
                
                // Mostrar datos extraídos
                displayExtractedData(originalResult, improvedResult);
                
                showLoading(false);
                
            } catch (error) {
                showLoading(false);
                alert('Error cargando FORMATO.pdf: ' + error.message);
            }
        }
        
        async function testOnlyImproved() {
            showLoading(true, 'Probando solo procesador mejorado...');
            
            try {
                const response = await fetch('FORMATO.pdf');
                const blob = await response.blob();
                const file = new File([blob], 'FORMATO.pdf', { type: 'application/pdf' });
                
                const improvedProcessor = new ImprovedPDFProcessor();
                const result = await improvedProcessor.processPDF(file);
                
                document.getElementById('improvedResults').innerHTML = `
                    <div class="success">✅ Procesamiento exitoso</div>
                    <p><strong>Tiempo:</strong> ${result.processingTime}ms</p>
                    <p><strong>Páginas:</strong> ${result.pageCount}</p>
                    <p><strong>Caracteres:</strong> ${result.rawText.length}</p>
                `;
                
                document.getElementById('originalResults').innerHTML = `
                    <div class="warning">⚠️ No ejecutado en esta prueba</div>
                `;
                
                rawTextData = result.rawText;
                displayExtractedData(null, result);
                
                showLoading(false);
                
            } catch (error) {
                showLoading(false);
                document.getElementById('improvedResults').innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                `;
            }
        }
        
        function displayExtractedData(originalResult, improvedResult) {
            // Mostrar datos del procesador original
            if (originalResult && originalResult.extractedData) {
                const data = originalResult.extractedData;
                document.getElementById('originalData').innerHTML = `
Cliente: ${data.cliente}
Fecha: ${data.fecha}
No. Cotización: ${data.numeroCotizacion}
Artículos (${data.articulos.length}):
${data.articulos.map(item => `  • ${item.cantidad} x ${item.descripcion} - $${item.precio}`).join('\n')}

Totales:
  Subtotal: $${data.totales.subtotal || 'N/A'}
  IVA: $${data.totales.iva || 'N/A'}
  Total: $${data.totales.total || 'N/A'}
                `;
            } else {
                document.getElementById('originalData').textContent = 'No hay datos o error en procesamiento';
            }
            
            // Mostrar datos del procesador mejorado
            if (improvedResult && improvedResult.extractedData) {
                const data = improvedResult.extractedData;
                document.getElementById('improvedData').innerHTML = `
Cliente: ${data.cliente}
Fecha: ${data.fecha}
No. Cotización: ${data.numeroCotizacion}
Artículos (${data.articulos.length}):
${data.articulos.map(item => `  • ${item.cantidad} x ${item.descripcion} - $${item.precio}`).join('\n')}

Totales:
  Subtotal: $${data.totales.subtotal || 'N/A'}
  IVA: $${data.totales.iva || 'N/A'}
  Total: $${data.totales.total || 'N/A'}
  Descuento: $${data.totales.descuento || 'N/A'}

Información adicional:
  Caracteres de texto: ${data.rawTextLength}
                `;
            } else {
                document.getElementById('improvedData').textContent = 'No hay datos o error en procesamiento';
            }
        }
        
        function showRawText() {
            if (rawTextData) {
                document.getElementById('rawTextContainer').textContent = rawTextData;
            } else {
                document.getElementById('rawTextContainer').textContent = 'Primero ejecuta una prueba para extraer el texto';
            }
        }
        
        // Ejecutar automáticamente al cargar
        window.addEventListener('load', () => {
            setTimeout(() => {
                testOnlyImproved();
            }, 1000);
        });
    </script>
</body>
</html>

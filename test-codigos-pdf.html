<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Test Códigos PDF - Diagnóstico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; }
        .error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Códigos PDF - Diagnóstico</h1>
        <p>Diagnóstico específico para el archivo <strong>codigos.pdf</strong> para verificar por qué no se detectan códigos.</p>
        
        <div class="info">
            <strong>Objetivo:</strong> Procesar codigos.pdf y verificar:<br>
            ✅ Si se extrae texto correctamente<br>
            ✅ Si se detecta columna de "Artículo" en el PDF<br>
            ✅ Si se extraen códigos de los artículos<br>
            ✅ Si la función hasArticleCodes() funciona<br>
            ✅ Si aparece el botón de exportar PDF
        </div>

        <div class="success">
            <strong>Nueva Lógica Implementada:</strong><br>
            🔍 <strong>detectarColumnaArticulo()</strong> - Busca encabezados como "Artículo", "Código", "Art.", etc.<br>
            📋 <strong>extraerCodigoDeLinea()</strong> - Extrae códigos solo si hay columna específica<br>
            ✅ <strong>Inteligente:</strong> Solo extrae códigos si detecta columna, sino deja en blanco
        </div>
        
        <button class="btn btn-primary" onclick="testCodigosPDF()">🚀 Procesar codigos.pdf</button>
        <button class="btn btn-secondary" onclick="testDeteccionCodigos()">🔍 Test Detección de Códigos</button>
    </div>

    <div class="container">
        <h2>📊 Resultado del Procesamiento</h2>
        <div id="resultadoProcesamiento" class="result">Haz clic en "Procesar codigos.pdf" para comenzar...</div>
    </div>

    <div class="container">
        <h2>🔧 Diagnóstico de Detección</h2>
        <div id="diagnosticoDeteccion" class="result">Esperando diagnóstico...</div>
    </div>

    <!-- Cargar librerías -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="js/pdf-processor-improved.js"></script>
    <script src="js/pdf-exporter.js"></script>
    
    <script>
        async function testCodigosPDF() {
            let results = '=== PROCESAMIENTO DE CODIGOS.PDF ===\n\n';
            
            try {
                results += '🔄 Cargando codigos.pdf...\n';
                document.getElementById('resultadoProcesamiento').textContent = results;
                
                // Cargar el PDF
                const response = await fetch('codigos.pdf');
                if (!response.ok) {
                    throw new Error(`Error cargando PDF: ${response.status}`);
                }
                
                const blob = await response.blob();
                const file = new File([blob], 'codigos.pdf', { type: 'application/pdf' });
                
                results += `✅ PDF cargado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)\n`;
                document.getElementById('resultadoProcesamiento').textContent = results;
                
                // Procesar con el procesador mejorado
                results += '🔄 Procesando con ImprovedPDFProcessor...\n';
                document.getElementById('resultadoProcesamiento').textContent = results;
                
                const processor = new ImprovedPDFProcessor();
                const result = await processor.processPDF(file);
                
                results += '✅ Procesamiento completado\n\n';
                
                // Analizar resultados
                results += '=== ANÁLISIS DE RESULTADOS ===\n';
                results += `Error: ${result.error || 'Ninguno'}\n`;
                results += `Archivo: ${result.fileName}\n`;
                results += `Tiempo: ${result.processingTime}ms\n\n`;
                
                if (result.extractedData) {
                    const data = result.extractedData;
                    results += '=== DATOS EXTRAÍDOS ===\n';
                    results += `Cliente: ${data.nombre || 'N/A'}\n`;
                    results += `Fecha: ${data.fecha || 'N/A'}\n`;
                    results += `OrdenRepuestos: ${data.ordenRepuestos || 'N/A'}\n`;
                    results += `Artículos encontrados: ${data.articulos ? data.articulos.length : 0}\n\n`;
                    
                    if (data.articulos && data.articulos.length > 0) {
                        results += '=== ARTÍCULOS DETALLADOS ===\n';
                        data.articulos.forEach((art, i) => {
                            results += `${i+1}. Cantidad: ${art.cantidad}\n`;
                            results += `   Artículo/Código: "${art.articulo}"\n`;
                            results += `   Descripción: "${art.descripcion}"\n`;
                            results += `   T.Facturación: ${art.tFacturacion}\n`;
                            results += `   PrecioUni: ${art.precioUni}\n`;
                            results += `   Total: ${art.total}\n\n`;
                        });

                        // Test de detección de códigos
                        results += '=== TEST DETECCIÓN DE CÓDIGOS ===\n';
                        const pdfExporter = new PDFExporter();
                        const hasArticleCodes = pdfExporter.hasArticleCodes([result]);

                        results += `hasArticleCodes([result]): ${hasArticleCodes}\n`;
                        results += `Botón PDF debería aparecer: ${hasArticleCodes ? 'SÍ' : 'NO'}\n\n`;

                        // Análisis individual de códigos
                        results += '=== ANÁLISIS INDIVIDUAL ===\n';
                        data.articulos.forEach((art, i) => {
                            const tieneCodigoNoVacio = art.articulo && art.articulo.trim() !== '';
                            results += `${i+1}. "${art.articulo}" → ${tieneCodigoNoVacio ? '✅ TIENE CÓDIGO' : '❌ SIN CÓDIGO'}\n`;
                        });

                        // Contar códigos
                        const codigosEncontrados = data.articulos.filter(art => art.articulo && art.articulo.trim() !== '').length;
                        results += `\n📊 RESUMEN: ${codigosEncontrados}/${data.articulos.length} artículos tienen código\n`;

                    } else {
                        results += '❌ No se encontraron artículos\n';
                    }
                } else {
                    results += '❌ No se extrajeron datos\n';
                }
                
            } catch (error) {
                results += `❌ Error: ${error.message}\n`;
                console.error('Error en test:', error);
            }
            
            document.getElementById('resultadoProcesamiento').textContent = results;
        }
        
        function testDeteccionCodigos() {
            let diagnostico = '=== DIAGNÓSTICO DE DETECCIÓN DE CÓDIGOS ===\n\n';
            
            // Test con datos simulados
            const testData = [
                {
                    fileName: 'test1.pdf',
                    extractedData: {
                        articulos: [
                            { articulo: 'ABC123', descripcion: 'FILTRO DE ACEITE' },
                            { articulo: '', descripcion: 'PASTILLAS DE FRENO' },
                            { articulo: 'XYZ789', descripcion: 'BUJÍA DE ENCENDIDO' }
                        ]
                    }
                },
                {
                    fileName: 'test2.pdf',
                    extractedData: {
                        articulos: [
                            { articulo: '', descripcion: 'ACEITE MOTOR' },
                            { articulo: null, descripcion: 'FILTRO AIRE' }
                        ]
                    }
                }
            ];
            
            const pdfExporter = new PDFExporter();
            
            diagnostico += '🧪 TEST 1: Datos con códigos\n';
            const test1 = pdfExporter.hasArticleCodes([testData[0]]);
            diagnostico += `Resultado: ${test1} (esperado: true)\n`;
            diagnostico += `Estado: ${test1 ? '✅ CORRECTO' : '❌ FALLO'}\n\n`;
            
            diagnostico += '🧪 TEST 2: Datos sin códigos\n';
            const test2 = pdfExporter.hasArticleCodes([testData[1]]);
            diagnostico += `Resultado: ${test2} (esperado: false)\n`;
            diagnostico += `Estado: ${test2 ? '❌ FALLO' : '✅ CORRECTO'}\n\n`;
            
            diagnostico += '🧪 TEST 3: Datos mixtos\n';
            const test3 = pdfExporter.hasArticleCodes(testData);
            diagnostico += `Resultado: ${test3} (esperado: true)\n`;
            diagnostico += `Estado: ${test3 ? '✅ CORRECTO' : '❌ FALLO'}\n\n`;
            
            diagnostico += '=== CÓDIGO DE LA FUNCIÓN ===\n';
            diagnostico += 'hasArticleCodes(processedData) {\n';
            diagnostico += '  for (const item of processedData) {\n';
            diagnostico += '    if (item.extractedData && item.extractedData.articulos) {\n';
            diagnostico += '      for (const articulo of item.extractedData.articulos) {\n';
            diagnostico += '        if (articulo.articulo && articulo.articulo.trim() !== "") {\n';
            diagnostico += '          return true;\n';
            diagnostico += '        }\n';
            diagnostico += '      }\n';
            diagnostico += '    }\n';
            diagnostico += '  }\n';
            diagnostico += '  return false;\n';
            diagnostico += '}\n\n';
            
            diagnostico += '=== CONCLUSIÓN ===\n';
            if (test1 && !test2 && test3) {
                diagnostico += '✅ La función hasArticleCodes() funciona correctamente\n';
                diagnostico += '🔍 El problema debe estar en la extracción de códigos del PDF\n';
            } else {
                diagnostico += '❌ La función hasArticleCodes() tiene problemas\n';
            }
            
            document.getElementById('diagnosticoDeteccion').textContent = diagnostico;
        }
        
        // Ejecutar test automáticamente
        window.addEventListener('load', () => {
            setTimeout(() => {
                testDeteccionCodigos();
                setTimeout(testCodigosPDF, 1000);
            }, 500);
        });
    </script>
</body>
</html>

/* ===== VARIABLES CSS ===== */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* ===== GENERAL STYLES ===== */
body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* ===== DROP ZONE STYLES ===== */
.drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background-color: #fafbfc;
}

.drop-zone:hover,
.drop-zone.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.drop-zone.dragover {
    transform: scale(1.02);
}

/* ===== FILE LIST STYLES ===== */
.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.file-item:hover {
    background-color: var(--light-color);
}

.file-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.file-icon {
    color: #dc3545;
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.status-processing {
    background-color: #cff4fc;
    color: #055160;
}

.status-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* ===== STATISTICS STYLES ===== */
.stat-item {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== TABLE STYLES ===== */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02);
}

/* ===== BUTTON STYLES ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* ===== PROGRESS STYLES ===== */
.progress {
    height: 0.5rem;
    border-radius: var(--border-radius);
}

.progress-bar {
    transition: width 0.3s ease;
}

/* ===== MODAL STYLES ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .drop-zone {
        padding: 1.5rem 1rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .file-status {
        align-self: flex-end;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
}

/* ===== ANIMATION STYLES ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== UTILITY CLASSES ===== */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* ===== CUSTOM SCROLLBAR ===== */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== LOADING STATES ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
